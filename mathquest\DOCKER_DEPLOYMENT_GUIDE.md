# 🐳 MathQuest Docker Deployment Guide

## ✅ Deployment Status: SUCCESSFUL

Your MathQuest application is now **successfully deployed** and running on Docker! 🎉

## 🚀 Current Status

- ✅ **Docker Containers**: Running successfully
- ✅ **Database**: MongoDB initialized with sample data
- ✅ **Application**: Accessible at http://localhost:3000
- ✅ **Sample Data**: 2 users, 5 achievements, 2 progress records loaded

## 🔧 What Was Fixed

### 1. Environment Configuration
- ✅ Proper MongoDB connection string configured
- ✅ Authentication credentials set up correctly
- ✅ NextAuth.js configuration completed

### 2. Database Initialization
- ✅ MongoDB container running with authentication
- ✅ Sample users created (<PERSON>, <PERSON>)
- ✅ Achievement system initialized
- ✅ Progress tracking data loaded
- ✅ Database indexes created for performance

### 3. Application Connectivity
- ✅ Next.js application connecting to MongoDB successfully
- ✅ All routes responding correctly (/, /grades, /grade/1, /demo)
- ✅ HTTP 200 status confirmed

## 🎮 How to Access the Application

### 1. Open Your Browser
Navigate to: **http://localhost:3000**

### 2. Sample Login Credentials
| User | Email | Password | Grade | Role |
|------|-------|----------|-------|------|
| <PERSON> | <EMAIL> | password123 | 1 | Student |
| Liam Smith | <EMAIL> | password123 | 2 | Student |

### 3. Available Features
- ✅ Grade selection (1-10)
- ✅ Interactive curriculum
- ✅ Achievement system
- ✅ Progress tracking
- ✅ Demo mode

## 🛠️ Docker Commands Reference

### Start the Application
```bash
docker-compose up -d
```

### Stop the Application
```bash
docker-compose down
```

### View Logs
```bash
# Application logs
docker logs mathquest-app

# Database logs
docker logs mathquest-mongodb
```

### Restart Services
```bash
docker-compose restart
```

### Check Container Status
```bash
docker ps --filter "name=mathquest"
```

## 🗄️ Database Management

### Access MongoDB Shell
```bash
docker exec -it mathquest-mongodb mongosh -u admin -p password123 --authenticationDatabase admin mathquest
```

### Common Database Commands
```javascript
// View all users
db.users.find().pretty()

// View all achievements
db.achievements.find().pretty()

// View progress data
db.progress.find().pretty()

// Count documents
db.users.countDocuments()
db.achievements.countDocuments()
db.progress.countDocuments()
```

### Reset Database (if needed)
```bash
# Connect to MongoDB
docker exec -it mathquest-mongodb mongosh -u admin -p password123 --authenticationDatabase admin mathquest

# Drop and recreate collections
db.users.drop()
db.achievements.drop()
db.progress.drop()

# Re-run initialization script
# (Then manually insert sample data as shown in the guide)
```

## 🔍 Troubleshooting

### If Application Won't Start
1. **Check Docker is running**:
   ```bash
   docker --version
   docker-compose --version
   ```

2. **Check container status**:
   ```bash
   docker ps -a
   ```

3. **View container logs**:
   ```bash
   docker logs mathquest-app
   docker logs mathquest-mongodb
   ```

4. **Restart containers**:
   ```bash
   docker-compose down
   docker-compose up -d
   ```

### If Database Connection Fails
1. **Verify MongoDB is running**:
   ```bash
   docker exec mathquest-mongodb mongosh --eval "db.runCommand('ping')"
   ```

2. **Check environment variables**:
   ```bash
   docker exec mathquest-app env | grep MONGODB
   ```

3. **Recreate containers**:
   ```bash
   docker-compose down -v
   docker-compose up -d
   ```

### If Port 3000 is Busy
1. **Find what's using the port**:
   ```bash
   netstat -ano | findstr :3000
   ```

2. **Change port in docker-compose.yml**:
   ```yaml
   ports:
     - "3001:3000"  # Use port 3001 instead
   ```

## 📊 Performance Monitoring

### Check Resource Usage
```bash
docker stats mathquest-app mathquest-mongodb
```

### View Application Metrics
- **Memory Usage**: Monitor container memory consumption
- **CPU Usage**: Check processing load
- **Network**: Monitor database connections

## 🔒 Security Notes

### Production Deployment
For production deployment, make sure to:

1. **Change Default Passwords**:
   - Update MongoDB admin password
   - Change NextAuth secret key
   - Update sample user passwords

2. **Environment Variables**:
   - Use proper environment file (.env.production)
   - Set secure random secrets
   - Configure proper database credentials

3. **Network Security**:
   - Use Docker networks for container isolation
   - Configure firewall rules
   - Enable SSL/TLS for production

## 🎯 Next Steps

1. **Explore the Application**: Test all features with sample data
2. **Add More Data**: Expand curriculum and quiz content
3. **Customize**: Modify themes, content, and features
4. **Deploy to Production**: Use cloud services for live deployment
5. **Monitor**: Set up logging and monitoring for production use

## 📞 Support

If you encounter any issues:

1. **Check this guide** for common solutions
2. **Review container logs** for error messages
3. **Verify environment configuration**
4. **Test database connectivity**

---

**🎉 Congratulations! Your MathQuest application is successfully deployed and ready to use!**

Access it now at: **http://localhost:3000**
