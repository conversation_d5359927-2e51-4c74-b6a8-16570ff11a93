(()=>{var e={};e.id=436,e.ids=[436],e.modules={365:(e,t,s)=>{Promise.resolve().then(s.bind(s,3920))},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(1658);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1130:(e,t,s)=>{"use strict";s.d(t,{Zp:()=>i});var a=s(687),l=s(3210),n=s(4780);let i=(0,l.forwardRef)(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card",(0,l.forwardRef)(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",(0,l.forwardRef)(({className:e,...t},s)=>(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",(0,l.forwardRef)(({className:e,...t},s)=>(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",(0,l.forwardRef)(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t})).displayName="CardContent",(0,l.forwardRef)(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},1135:()=>{},2080:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},2318:(e,t,s)=>{Promise.resolve().then(s.bind(s,7552))},2582:(e,t,s)=>{Promise.resolve().then(s.bind(s,3622))},2643:(e,t,s)=>{"use strict";s.d(t,{$:()=>i});var a=s(687),l=s(3210),n=s(4780);let i=(0,l.forwardRef)(({className:e,variant:t="default",size:s="default",...l},i)=>(0,a.jsx)("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===t,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===t,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===t,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"hover:bg-accent hover:text-accent-foreground":"ghost"===t,"text-primary underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===s,"h-9 rounded-md px-3":"sm"===s,"h-11 rounded-md px-8":"lg"===s,"h-10 w-10":"icon"===s},e),ref:i,...l}));i.displayName="Button"},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3073:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_});var a=s(687),l=s(3210),n=s(6001),i=s(2688);let r=(0,i.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]),o=(0,i.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),d=(0,i.A)("shapes",[["path",{d:"M8.3 10a.7.7 0 0 1-.626-1.079L11.4 3a.7.7 0 0 1 1.198-.043L16.3 8.9a.7.7 0 0 1-.572 1.1Z",key:"1bo67w"}],["rect",{x:"3",y:"14",width:"7",height:"7",rx:"1",key:"1bkyp8"}],["circle",{cx:"17.5",cy:"17.5",r:"3.5",key:"w3z12y"}]]),c=(0,i.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),m=(0,i.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var x=s(4398),h=s(2080),u=s(7268),p=s(2643),g=s(1130),f=s(2157),j=s(2789),b=s(2743),y=s(1279),v=s(8171),N=s(201);class w extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,s=(0,v.s)(e)&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=t.offsetHeight||0,a.width=t.offsetWidth||0,a.top=t.offsetTop,a.left=t.offsetLeft,a.right=s-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function P({children:e,isPresent:t,anchorX:s}){let n=(0,l.useId)(),i=(0,l.useRef)(null),r=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:o}=(0,l.useContext)(N.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:a,top:l,left:d,right:c}=r.current;if(t||!i.current||!e||!a)return;let m="left"===s?`left: ${d}`:`right: ${c}`;i.current.dataset.motionPopId=n;let x=document.createElement("style");return o&&(x.nonce=o),document.head.appendChild(x),x.sheet&&x.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${m}px !important;
            top: ${l}px !important;
          }
        `),()=>{document.head.contains(x)&&document.head.removeChild(x)}},[t]),(0,a.jsx)(w,{isPresent:t,childRef:i,sizeRef:r,children:l.cloneElement(e,{ref:i})})}let k=({children:e,initial:t,isPresent:s,onExitComplete:n,custom:i,presenceAffectsLayout:r,mode:o,anchorX:d})=>{let c=(0,j.M)(M),m=(0,l.useId)(),x=!0,h=(0,l.useMemo)(()=>(x=!1,{id:m,initial:t,isPresent:s,custom:i,onExitComplete:e=>{for(let t of(c.set(e,!0),c.values()))if(!t)return;n&&n()},register:e=>(c.set(e,!1),()=>c.delete(e))}),[s,c,n]);return r&&x&&(h={...h}),(0,l.useMemo)(()=>{c.forEach((e,t)=>c.set(t,!1))},[s]),l.useEffect(()=>{s||c.size||!n||n()},[s]),"popLayout"===o&&(e=(0,a.jsx)(P,{isPresent:s,anchorX:d,children:e})),(0,a.jsx)(y.t.Provider,{value:h,children:e})};function M(){return new Map}var C=s(6044);let S=e=>e.key||"";function A(e){let t=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&t.push(e)}),t}let $=({children:e,custom:t,initial:s=!0,onExitComplete:n,presenceAffectsLayout:i=!0,mode:r="sync",propagate:o=!1,anchorX:d="left"})=>{let[c,m]=(0,C.xQ)(o),x=(0,l.useMemo)(()=>A(e),[e]),h=o&&!c?[]:x.map(S),u=(0,l.useRef)(!0),p=(0,l.useRef)(x),g=(0,j.M)(()=>new Map),[y,v]=(0,l.useState)(x),[N,w]=(0,l.useState)(x);(0,b.E)(()=>{u.current=!1,p.current=x;for(let e=0;e<N.length;e++){let t=S(N[e]);h.includes(t)?g.delete(t):!0!==g.get(t)&&g.set(t,!1)}},[N,h.length,h.join("-")]);let P=[];if(x!==y){let e=[...x];for(let t=0;t<N.length;t++){let s=N[t],a=S(s);h.includes(a)||(e.splice(t,0,s),P.push(s))}return"wait"===r&&P.length&&(e=P),w(A(e)),v(x),null}let{forceRender:M}=(0,l.useContext)(f.L);return(0,a.jsx)(a.Fragment,{children:N.map(e=>{let l=S(e),f=(!o||!!c)&&(x===N||h.includes(l));return(0,a.jsx)(k,{isPresent:f,initial:(!u.current||!!s)&&void 0,custom:t,presenceAffectsLayout:i,mode:r,onExitComplete:f?void 0:()=>{if(!g.has(l))return;g.set(l,!0);let e=!0;g.forEach(t=>{t||(e=!1)}),e&&(M?.(),w(p.current),o&&m?.(),n&&n())},anchorX:d,children:e},l)})})},T=(0,i.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),D=(0,i.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),E=[{id:"circle",name:"Circle",svg:'<circle cx="50" cy="50" r="40" />',color:"#FF6B6B"},{id:"square",name:"Square",svg:'<rect x="10" y="10" width="80" height="80" />',color:"#4ECDC4"},{id:"triangle",name:"Triangle",svg:'<polygon points="50,10 90,90 10,90" />',color:"#45B7D1"},{id:"rectangle",name:"Rectangle",svg:'<rect x="10" y="25" width="80" height="50" />',color:"#96CEB4"},{id:"star",name:"Star",svg:'<polygon points="50,5 61,35 95,35 68,57 79,91 50,70 21,91 32,57 5,35 39,35" />',color:"#FFEAA7"},{id:"heart",name:"Heart",svg:'<path d="M50,85 C50,85 20,60 20,40 C20,25 30,15 45,15 C47,15 50,20 50,20 C50,20 53,15 55,15 C70,15 80,25 80,40 C80,60 50,85 50,85 Z" />',color:"#FD79A8"}],q=[{id:"counting",title:"Counting Game",description:"Count objects and learn numbers 1-10",icon:(0,a.jsx)(r,{className:"h-8 w-8"}),grade:"Grade 1",difficulty:"Easy",color:"from-blue-400 to-blue-600",component:function({maxNumber:e=10,onComplete:t}){let[s,i]=(0,l.useState)(1),[r,o]=(0,l.useState)(0),[d,c]=(0,l.useState)(null),[m,h]=(0,l.useState)(null),[f,j]=(0,l.useState)(!1),[b,y]=(0,l.useState)(!1),v=e=>{let t=["\uD83C\uDF4E","\uD83C\uDF1F","\uD83C\uDF88","\uD83D\uDC31","\uD83D\uDE97","\uD83C\uDF38","\uD83C\uDF81","\uD83E\uDD8B"],s=t[Math.floor(Math.random()*t.length)];return Array(e).fill(s)},[N,w]=(0,l.useState)(v(s)),P=t=>{let s=[t];for(;s.length<4;){let t=Math.floor(Math.random()*e)+1;s.includes(t)||s.push(t)}return s.sort(()=>Math.random()-.5)},[k,M]=(0,l.useState)(P(s)),C=a=>{c(a);let l=a===s;h(l),l&&(o(r+10),y(!0),setTimeout(()=>y(!1),1e3)),setTimeout(()=>{s>=e?(j(!0),t?.(r+10*!!l)):S()},1500)},S=()=>{let e=s+1;i(e),w(v(e)),M(P(e)),c(null),h(null)};return f?(0,a.jsxs)(g.Zp,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(n.P.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Congratulations!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You completed the counting game!"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-100 to-blue-100 p-4 rounded-lg mb-6",children:(0,a.jsxs)("p",{className:"text-lg font-semibold text-purple-700",children:["Final Score: ",r," points"]})})]}),(0,a.jsxs)(p.$,{onClick:()=>{i(1),o(0),c(null),h(null),j(!1),w(v(1)),M(P(1))},className:"w-full",children:[(0,a.jsx)(T,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(g.Zp,{className:"p-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",r]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Question ",s," of ",e]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Count the objects below:"}),(0,a.jsx)("div",{className:"grid grid-cols-5 gap-4 justify-items-center mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg",children:(0,a.jsx)($,{children:N.map((e,t)=>(0,a.jsx)(n.P.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:.1*t},className:"text-4xl",children:e},t))})})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:k.map(e=>(0,a.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(p.$,{onClick:()=>C(e),disabled:null!==d,variant:d===e?m?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:e})},e))}),(0,a.jsx)($,{children:null!==m&&(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${m?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:m?"\uD83C\uDF89 Correct! Well done!":"❌ Try again next time!"})}),(0,a.jsx)($,{children:b&&(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(n.P.div,{animate:{scale:[1,1.2,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:"⭐"})})})]})}},{id:"addition",title:"Addition Challenge",description:"Practice addition with fun problems",icon:(0,a.jsx)(o,{className:"h-8 w-8"}),grade:"Grade 1-2",difficulty:"Easy",color:"from-green-400 to-green-600",component:function({maxNumber:e=10,onComplete:t}){let[s,i]=(0,l.useState)(1),[r,o]=(0,l.useState)(0),[d,c]=(0,l.useState)(null),[m,h]=(0,l.useState)(null),[f,j]=(0,l.useState)(!1),[b,y]=(0,l.useState)(!1),v=()=>{let t=Math.floor(Math.random()*e)+1,s=Math.floor(Math.random()*e)+1;return{num1:t,num2:s,answer:t+s}},[N,w]=(0,l.useState)(v()),P=e=>{let t=[e];for(;t.length<4;){let s=e+Math.floor(6*Math.random())-3;s>0&&!t.includes(s)&&t.push(s)}return t.sort(()=>Math.random()-.5)},[k,M]=(0,l.useState)(P(N.answer)),C=e=>{c(e);let a=e===N.answer;h(a),a&&(o(r+10),y(!0),setTimeout(()=>y(!1),1e3)),setTimeout(()=>{s>=10?(j(!0),t?.(r+10*!!a)):S()},1500)},S=()=>{let e=v();i(s+1),w(e),M(P(e.answer)),c(null),h(null)};return f?(0,a.jsxs)(g.Zp,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(n.P.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Amazing Work!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You completed the addition challenge!"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-100 to-blue-100 p-4 rounded-lg mb-6",children:(0,a.jsxs)("p",{className:"text-lg font-semibold text-green-700",children:["Final Score: ",r," points"]})})]}),(0,a.jsxs)(p.$,{onClick:()=>{i(1),o(0),c(null),h(null),j(!1);let e=v();w(e),M(P(e.answer))},className:"w-full",children:[(0,a.jsx)(T,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(g.Zp,{className:"p-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",r]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Problem ",s," of 10"]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Solve the Addition Problem:"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-blue-50 p-8 rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4 text-6xl font-bold text-purple-700",children:[(0,a.jsx)(n.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.2},children:N.num1}),(0,a.jsx)(n.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.4},children:(0,a.jsx)(D,{className:"h-12 w-12"})}),(0,a.jsx)(n.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.6},children:N.num2}),(0,a.jsx)(n.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.8},children:"="}),(0,a.jsx)(n.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:1},className:"text-gray-400",children:"?"})]})})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:k.map(e=>(0,a.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(p.$,{onClick:()=>C(e),disabled:null!==d,variant:d===e?m?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:e})},e))}),(0,a.jsx)($,{children:null!==m&&(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${m?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:m?"\uD83C\uDF89 Correct! Great job!":`❌ The answer was ${N.answer}. Keep trying!`})}),(0,a.jsx)($,{children:b&&(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(n.P.div,{animate:{scale:[1,1.5,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:"\uD83C\uDF1F"})})})]})}},{id:"shapes",title:"Shape Matching",description:"Identify and match different shapes",icon:(0,a.jsx)(d,{className:"h-8 w-8"}),grade:"Grade 1",difficulty:"Easy",color:"from-purple-400 to-purple-600",component:function({onComplete:e}){let[t,s]=(0,l.useState)(1),[i,r]=(0,l.useState)(0),[o,c]=(0,l.useState)(E[0]),[m,h]=(0,l.useState)(null),[f,j]=(0,l.useState)(null),[b,y]=(0,l.useState)(!1),[v,N]=(0,l.useState)(!1),w=e=>{let t=[e],s=E.filter(t=>t.id!==e.id);for(;t.length<4&&s.length>0;){let e=Math.floor(Math.random()*s.length),a=s.splice(e,1)[0];t.push(a)}return t.sort(()=>Math.random()-.5)},[P,k]=(0,l.useState)(w(o)),M=s=>{h(s);let a=s.id===o.id;j(a),a&&(r(i+15),N(!0),setTimeout(()=>N(!1),1e3)),setTimeout(()=>{t>=8?(y(!0),e?.(i+15*!!a)):C()},1500)},C=()=>{let e=E[Math.floor(Math.random()*E.length)];s(t+1),c(e),k(w(e)),h(null),j(null)};return b?(0,a.jsxs)(g.Zp,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(n.P.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Shape Master!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You've mastered shape recognition!"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-100 to-pink-100 p-4 rounded-lg mb-6",children:(0,a.jsxs)("p",{className:"text-lg font-semibold text-purple-700",children:["Final Score: ",i," points"]})})]}),(0,a.jsxs)(p.$,{onClick:()=>{s(1),r(0),h(null),j(null),y(!1);let e=E[Math.floor(Math.random()*E.length)];c(e),k(w(e))},className:"w-full",children:[(0,a.jsx)(T,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(g.Zp,{className:"p-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",i]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Round ",t," of 8"]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(d,{className:"h-8 w-8 text-purple-600 mr-2"}),(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-800",children:["Find the ",o.name,"!"]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg mb-6",children:[(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:"Click on the shape that matches:"}),(0,a.jsx)(n.P.div,{initial:{scale:0},animate:{scale:1},className:"flex justify-center",children:(0,a.jsx)("svg",{width:"120",height:"120",className:"drop-shadow-lg",children:(0,a.jsx)("g",{fill:o.color,stroke:"#333",strokeWidth:"2",dangerouslySetInnerHTML:{__html:o.svg}})})}),(0,a.jsx)("p",{className:"text-xl font-bold text-purple-700 mt-4",children:o.name})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:P.map((e,t)=>(0,a.jsx)(n.P.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:.1*t},whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsxs)(p.$,{onClick:()=>M(e),disabled:null!==m,variant:m?.id===e.id?f?"default":"destructive":"outline",className:"w-full h-32 p-4 flex flex-col items-center justify-center",children:[(0,a.jsx)("svg",{width:"60",height:"60",className:"mb-2",children:(0,a.jsx)("g",{fill:e.color,stroke:"#333",strokeWidth:"1",dangerouslySetInnerHTML:{__html:e.svg}})}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.name})]})},e.id))}),(0,a.jsx)($,{children:null!==f&&(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${f?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:f?"\uD83C\uDF89 Perfect! You found the right shape!":`❌ That was a ${m?.name}. Look for the ${o.name}!`})}),(0,a.jsx)($,{children:v&&(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(n.P.div,{animate:{scale:[1,1.3,1],rotate:[0,180,360]},transition:{duration:1},className:"text-6xl",children:"\uD83C\uDFA8"})})})]})}},{id:"sequences",title:"Number Patterns",description:"Complete number sequences and patterns",icon:(0,a.jsx)(c,{className:"h-8 w-8"}),grade:"Grade 2-3",difficulty:"Medium",color:"from-indigo-400 to-indigo-600",component:function({maxNumber:e=20,onComplete:t}){let[s,i]=(0,l.useState)(1),[r,o]=(0,l.useState)(0),[d,m]=(0,l.useState)(null),[h,f]=(0,l.useState)(null),[j,b]=(0,l.useState)(!1),[y,v]=(0,l.useState)(!1),N=()=>{let t=["counting","skip2","skip5","skip10"],s=t[Math.floor(Math.random()*t.length)],a=[],l=1,n=1;switch(s){case"counting":l=1,n=Math.floor(Math.random()*(e-6))+1;break;case"skip2":l=2,n=2*Math.floor(5*Math.random())+2;break;case"skip5":l=5,n=5;break;case"skip10":l=10,n=10}for(let e=0;e<5;e++)a.push(n+e*l);let i=Math.floor(3*Math.random())+1,r=a[i];return{sequence:a.map((e,t)=>t===i?null:e),missingNumber:r,missingIndex:i,step:l,type:s}},[w,P]=(0,l.useState)(N()),k=t=>{let s=[t];for(;s.length<4;){let a=t+(Math.floor(6*Math.random())-3);a>0&&a<=e&&!s.includes(a)&&s.push(a)}return s.sort(()=>Math.random()-.5)},[M,C]=(0,l.useState)(k(w.missingNumber)),S=e=>{m(e);let a=e===w.missingNumber;f(a),a&&(o(r+20),v(!0),setTimeout(()=>v(!1),1e3)),setTimeout(()=>{s>=8?(b(!0),t?.(r+20*!!a)):A()},1500)},A=()=>{let e=N();i(s+1),P(e),C(k(e.missingNumber)),m(null),f(null)};return j?(0,a.jsxs)(g.Zp,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(n.P.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Pattern Master!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You've mastered number patterns!"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-100 to-purple-100 p-4 rounded-lg mb-6",children:(0,a.jsxs)("p",{className:"text-lg font-semibold text-blue-700",children:["Final Score: ",r," points"]})})]}),(0,a.jsxs)(p.$,{onClick:()=>{i(1),o(0),m(null),f(null),b(!1);let e=N();P(e),C(k(e.missingNumber))},className:"w-full",children:[(0,a.jsx)(T,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(g.Zp,{className:"p-6 max-w-3xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",r]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Problem ",s," of 8"]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Complete the Number Pattern"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:((e,t)=>{switch(e){case"counting":return"Counting by 1s";case"skip2":return"Counting by 2s";case"skip5":return"Counting by 5s";case"skip10":return"Counting by 10s";default:return`Counting by ${t}s`}})(w.type,w.step)}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg mb-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center space-x-4",children:w.sequence.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2*t},className:`w-16 h-16 rounded-lg flex items-center justify-center text-2xl font-bold ${null===e?"bg-yellow-200 border-2 border-dashed border-yellow-400 text-yellow-600":"bg-white border-2 border-blue-300 text-blue-700"}`,children:null===e?"?":e}),t<w.sequence.length-1&&(0,a.jsx)(c,{className:"h-6 w-6 text-gray-400 mx-2"})]},t))})})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:M.map(e=>(0,a.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(p.$,{onClick:()=>S(e),disabled:null!==d,variant:d===e?h?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:e})},e))}),(0,a.jsx)($,{children:null!==h&&(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${h?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:h?"\uD83C\uDF89 Excellent! You found the pattern!":`❌ The missing number was ${w.missingNumber}. Keep practicing patterns!`})}),(0,a.jsx)($,{children:y&&(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(n.P.div,{animate:{scale:[1,1.4,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:"\uD83E\uDDE9"})})})]})}},{id:"multiplication",title:"Times Tables",description:"Master multiplication with visual aids",icon:(0,a.jsx)(m,{className:"h-8 w-8"}),grade:"Grade 3-4",difficulty:"Hard",color:"from-orange-400 to-orange-600",component:function({maxTable:e=10,onComplete:t}){let[s,i]=(0,l.useState)(1),[r,o]=(0,l.useState)(0),[d,c]=(0,l.useState)(null),[h,f]=(0,l.useState)(null),[j,b]=(0,l.useState)(!1),[y,v]=(0,l.useState)(!1),[N,w]=(0,l.useState)(0),P=()=>{let t=Math.floor(Math.random()*e)+1,s=Math.floor(Math.random()*e)+1;return{num1:t,num2:s,answer:t*s}},[k,M]=(0,l.useState)(P()),C=e=>{let t=[e];for(;t.length<4;){let s=[e+k.num1,e+k.num2,e-k.num1,e-k.num2,(k.num1+1)*k.num2,k.num1*(k.num2+1),(k.num1-1)*k.num2,k.num1*(k.num2-1)],a=s[Math.floor(Math.random()*s.length)];a>0&&a!==e&&!t.includes(a)&&t.push(a)}return t.sort(()=>Math.random()-.5)},[S,A]=(0,l.useState)(C(k.answer)),D=e=>{c(e);let a=e===k.answer;f(a),a?(o(r+(15+5*N)),w(N+1),v(!0),setTimeout(()=>v(!1),1e3)):w(0),setTimeout(()=>{s>=12?(b(!0),t?.(r+(a?15+5*N:0))):E()},1500)},E=()=>{let e=P();i(s+1),M(e),A(C(e.answer)),c(null),f(null)};return j?(0,a.jsxs)(g.Zp,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(n.P.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Multiplication Master!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You've conquered the times tables!"}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-orange-100 to-red-100 p-4 rounded-lg mb-6",children:[(0,a.jsxs)("p",{className:"text-lg font-semibold text-orange-700",children:["Final Score: ",r," points"]}),N>3&&(0,a.jsxs)("p",{className:"text-sm text-orange-600 mt-1",children:["\uD83D\uDD25 Best streak: ",N," in a row!"]})]})]}),(0,a.jsxs)(p.$,{onClick:()=>{i(1),o(0),w(0),c(null),f(null),b(!1);let e=P();M(e),A(C(e.answer))},className:"w-full",children:[(0,a.jsx)(T,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(g.Zp,{className:"p-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",r]})]}),N>0&&(0,a.jsx)("div",{className:"flex items-center space-x-1 bg-orange-100 px-3 py-1 rounded-full",children:(0,a.jsxs)("span",{className:"text-orange-600 font-semibold",children:["\uD83D\uDD25 ",N]})})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Problem ",s," of 12"]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Solve the Multiplication:"}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-red-50 p-8 rounded-lg mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4 text-6xl font-bold text-orange-700",children:[(0,a.jsx)(n.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.2},children:k.num1}),(0,a.jsx)(n.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.4},children:(0,a.jsx)(m,{className:"h-12 w-12"})}),(0,a.jsx)(n.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.6},children:k.num2}),(0,a.jsx)(n.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.8},children:"="}),(0,a.jsx)(n.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:1},className:"text-gray-400",children:"?"})]}),k.num1<=5&&k.num2<=5&&(0,a.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1.2},className:"mt-6",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Visual help:"}),(0,a.jsx)("div",{className:"flex flex-col items-center space-y-2",children:Array.from({length:k.num1},(e,t)=>(0,a.jsx)("div",{className:"flex space-x-2",children:Array.from({length:k.num2},(e,t)=>(0,a.jsx)("div",{className:"w-4 h-4 bg-orange-300 rounded-full"},t))},t))})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:S.map(e=>(0,a.jsx)(n.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(p.$,{onClick:()=>D(e),disabled:null!==d,variant:d===e?h?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:e})},e))}),(0,a.jsx)($,{children:null!==h&&(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${h?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:h?N>1?`🎉 Correct! Amazing streak of ${N}!`:"\uD83C\uDF89 Correct! Great job!":`❌ The answer was ${k.answer}. Remember: ${k.num1} \xd7 ${k.num2} = ${k.answer}`})}),(0,a.jsx)($,{children:y&&(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(n.P.div,{animate:{scale:[1,1.5,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:N>3?"\uD83D\uDD25":"⭐"})})})]})}}];function z({onGameComplete:e}){let[t,s]=(0,l.useState)(null),[i,r]=(0,l.useState)(0),[o,d]=(0,l.useState)(new Set),c=e=>{s(e)},m=e=>{switch(e){case"Easy":return"bg-green-100 text-green-800";case"Medium":return"bg-yellow-100 text-yellow-800";case"Hard":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};if(t){let l=t.component;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(p.$,{variant:"ghost",onClick:()=>{s(null)},children:"← Back to Games"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:t.title}),(0,a.jsx)("p",{className:"text-gray-600",children:t.description})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-yellow-500"}),(0,a.jsxs)("span",{className:"font-semibold",children:["Total: ",i]})]})]}),(0,a.jsx)(l,{onComplete:a=>{t&&(r(i+a),d(e=>new Set([...e,t.id])),e?.(t.id,a),s(null))}})]})}return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-purple-600 mr-3"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Math Games"})]}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"Choose a game to practice your math skills!"}),i>0&&(0,a.jsxs)(n.P.div,{initial:{scale:0},animate:{scale:1},className:"bg-gradient-to-r from-yellow-100 to-orange-100 p-4 rounded-lg mb-6 inline-block",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-6 w-6 text-yellow-600"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-yellow-700",children:["Total Score: ",i," points"]})]}),(0,a.jsxs)("p",{className:"text-sm text-yellow-600",children:["Games completed: ",o.size,"/",q.length]})]})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:q.map((e,t)=>(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,a.jsxs)(g.Zp,{className:"p-6 h-full cursor-pointer hover:shadow-lg transition-all relative overflow-hidden",children:[o.has(e.id)&&(0,a.jsx)("div",{className:"absolute top-2 right-2 bg-green-500 text-white rounded-full p-1",children:(0,a.jsx)(x.A,{className:"h-4 w-4 fill-current"})}),(0,a.jsx)("div",{className:`bg-gradient-to-r ${e.color} p-4 rounded-lg mb-4 text-white w-fit`,children:e.icon}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:e.grade}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${m(e.difficulty)}`,children:e.difficulty})]}),(0,a.jsx)(p.$,{onClick:()=>c(e),className:"w-full mt-4",variant:o.has(e.id)?"outline":"default",children:o.has(e.id)?"Play Again":"Start Game"})]})]})},e.id))}),o.size>0&&(0,a.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-white p-6 rounded-lg border",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-800 mb-4",children:"Your Progress"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Games Completed"}),(0,a.jsxs)("span",{className:"font-semibold",children:[o.size,"/",q.length]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500",style:{width:`${o.size/q.length*100}%`}})}),o.size===q.length&&(0,a.jsx)(n.P.p,{initial:{opacity:0},animate:{opacity:1},className:"text-green-600 font-semibold text-center mt-4",children:"\uD83C\uDF89 Congratulations! You've completed all games!"})]})]})]})}var R=s(5814),G=s.n(R),F=s(8559);function _(){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)(G(),{href:"/",children:(0,a.jsxs)(p.$,{variant:"ghost",className:"mb-4",children:[(0,a.jsx)(F.A,{className:"mr-2 h-4 w-4"}),"Back to Home"]})}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-center mb-4",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",children:"MathQuest Interactive Demo"})}),(0,a.jsx)("p",{className:"text-xl text-gray-600 text-center max-w-2xl mx-auto",children:"Try our collection of interactive math games designed for different grade levels. Choose a game and start learning!"})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(z,{onGameComplete:(e,t)=>{console.log(`Game ${e} completed with score: ${t}`)}})}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mt-12",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFAF"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Interactive Learning"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Engaging games that make learning math fun and memorable for children."})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDCCA"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Progress Tracking"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Real-time feedback and detailed progress reports for students and parents."})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFC6"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Achievement System"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Badges, points, and rewards that motivate students to keep learning."})]})]}),(0,a.jsxs)("div",{className:"text-center mt-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Ready to Start Learning?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Join thousands of students already improving their math skills with MathQuest."}),(0,a.jsxs)("div",{className:"space-x-4",children:[(0,a.jsx)(G(),{href:"/register",children:(0,a.jsx)(p.$,{size:"lg",children:"Get Started Free"})}),(0,a.jsx)(G(),{href:"/",children:(0,a.jsx)(p.$,{variant:"outline",size:"lg",children:"Learn More"})})]})]})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3622:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\components\\Providers.tsx","Providers")},3873:e=>{"use strict";e.exports=require("path")},3920:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\augument\\\\onstud\\\\mathquest\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx","default")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>r,viewport:()=>o});var a=s(7413),l=s(5041),n=s.n(l);s(1135);var i=s(3622);let r={title:"MathQuest - Gamified Mathematics Learning",description:"Interactive mathematics learning platform for grades 1-10 with games, achievements, and personalized progress tracking",keywords:"mathematics, learning, education, games, kids, math, interactive",authors:[{name:"MathQuest Team"}]},o={width:"device-width",initialScale:1};function d({children:e}){return(0,a.jsx)("html",{lang:"en",className:"h-full",children:(0,a.jsx)("body",{className:`${n().className} h-full bg-gradient-to-br from-blue-50 to-purple-50 antialiased`,children:(0,a.jsx)(i.Providers,{children:e})})})}},4761:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,5543,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(9384),l=s(2348);function n(...e){return(0,l.QP)((0,a.$)(e))}},7552:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>n});var a=s(687),l=s(2136);function n({children:e}){return(0,a.jsx)(l.SessionProvider,{children:e})}},7629:(e,t,s)=>{Promise.resolve().then(s.bind(s,3073))},7785:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},7950:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=s(5239),l=s(8088),n=s(8170),i=s.n(n),r=s(893),o={};for(let e in r)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>r[e]);s.d(t,o);let d={children:["",{children:["demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3920)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/demo/page",pathname:"/demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,70,567,135],()=>s(7950));module.exports=a})();