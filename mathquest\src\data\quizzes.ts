// Comprehensive Quiz System for MathQuest
// Questions organized by grade and week

export interface QuizOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'fill-in-blank' | 'drag-drop' | 'true-false' | 'matching';
  question: string;
  options?: QuizOption[];
  correctAnswer?: string | number;
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  imageUrl?: string;
  audioUrl?: string;
}

export interface WeeklyQuiz {
  grade: number;
  week: number;
  title: string;
  description: string;
  totalQuestions: number;
  timeLimit: number; // in minutes
  passingScore: number; // percentage
  questions: QuizQuestion[];
}

export interface GradeQuizzes {
  grade: number;
  title: string;
  totalWeeks: number;
  quizzes: WeeklyQuiz[];
}

export const mathQuizzes: GradeQuizzes[] = [
  {
    grade: 1,
    title: "Grade 1 Mathematics Quizzes",
    totalWeeks: 40,
    quizzes: [
      {
        grade: 1,
        week: 1,
        title: "Numbers 1-10 Quiz",
        description: "Test your knowledge of counting and recognizing numbers 1-10",
        totalQuestions: 10,
        timeLimit: 15,
        passingScore: 70,
        questions: [
          {
            id: "g1w1q1",
            type: "multiple-choice",
            question: "How many apples are shown in the picture?",
            options: [
              { id: "a", text: "3", isCorrect: false },
              { id: "b", text: "5", isCorrect: true },
              { id: "c", text: "7", isCorrect: false },
              { id: "d", text: "4", isCorrect: false }
            ],
            explanation: "Count each apple carefully. There are 5 apples in total.",
            difficulty: "easy",
            points: 10,
            imageUrl: "/images/quizzes/g1w1/5-apples.png"
          },
          {
            id: "g1w1q2",
            type: "multiple-choice",
            question: "Which number comes after 7?",
            options: [
              { id: "a", text: "6", isCorrect: false },
              { id: "b", text: "8", isCorrect: true },
              { id: "c", text: "9", isCorrect: false },
              { id: "d", text: "5", isCorrect: false }
            ],
            explanation: "When counting: 6, 7, 8, 9, 10. The number 8 comes after 7.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g1w1q3",
            type: "fill-in-blank",
            question: "Write the number that represents this many stars: ⭐⭐⭐",
            correctAnswer: 3,
            explanation: "Count the stars: 1, 2, 3. The answer is 3.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g1w1q4",
            type: "multiple-choice",
            question: "Which group has exactly 6 objects?",
            options: [
              { id: "a", text: "🔵🔵🔵🔵🔵", isCorrect: false },
              { id: "b", text: "🔵🔵🔵🔵🔵🔵", isCorrect: true },
              { id: "c", text: "🔵🔵🔵🔵🔵🔵🔵", isCorrect: false },
              { id: "d", text: "🔵🔵🔵🔵", isCorrect: false }
            ],
            explanation: "Count the blue circles in each option. Option B has exactly 6 circles.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g1w1q5",
            type: "true-false",
            question: "The number 10 is greater than the number 9.",
            correctAnswer: "true",
            explanation: "Yes, 10 comes after 9 in the counting sequence, so 10 is greater than 9.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g1w1q6",
            type: "multiple-choice",
            question: "What number is missing? 1, 2, 3, __, 5",
            options: [
              { id: "a", text: "3", isCorrect: false },
              { id: "b", text: "4", isCorrect: true },
              { id: "c", text: "6", isCorrect: false },
              { id: "d", text: "2", isCorrect: false }
            ],
            explanation: "The sequence is 1, 2, 3, 4, 5. The missing number is 4.",
            difficulty: "medium",
            points: 15
          },
          {
            id: "g1w1q7",
            type: "fill-in-blank",
            question: "How many fingers are on two hands?",
            correctAnswer: 10,
            explanation: "Each hand has 5 fingers. Two hands have 5 + 5 = 10 fingers.",
            difficulty: "medium",
            points: 15
          },
          {
            id: "g1w1q8",
            type: "multiple-choice",
            question: "Which number is the smallest?",
            options: [
              { id: "a", text: "8", isCorrect: false },
              { id: "b", text: "3", isCorrect: true },
              { id: "c", text: "6", isCorrect: false },
              { id: "d", text: "9", isCorrect: false }
            ],
            explanation: "When we count 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, the number 3 comes first among the choices.",
            difficulty: "medium",
            points: 15
          },
          {
            id: "g1w1q9",
            type: "drag-drop",
            question: "Arrange these numbers from smallest to largest: 7, 2, 9, 4",
            correctAnswer: "2, 4, 7, 9",
            explanation: "From smallest to largest: 2, 4, 7, 9",
            difficulty: "hard",
            points: 20
          },
          {
            id: "g1w1q10",
            type: "multiple-choice",
            question: "If you have 8 toys and you give away 3 toys, how many toys do you have left?",
            options: [
              { id: "a", text: "5", isCorrect: true },
              { id: "b", text: "6", isCorrect: false },
              { id: "c", text: "4", isCorrect: false },
              { id: "d", text: "11", isCorrect: false }
            ],
            explanation: "Start with 8 toys, take away 3 toys: 8 - 3 = 5 toys left.",
            difficulty: "hard",
            points: 20
          }
        ]
      },
      {
        grade: 1,
        week: 2,
        title: "Numbers 11-20 Quiz",
        description: "Test your knowledge of teen numbers and counting to 20",
        totalQuestions: 8,
        timeLimit: 12,
        passingScore: 70,
        questions: [
          {
            id: "g1w2q1",
            type: "multiple-choice",
            question: "What number comes after 15?",
            options: [
              { id: "a", text: "14", isCorrect: false },
              { id: "b", text: "16", isCorrect: true },
              { id: "c", text: "17", isCorrect: false },
              { id: "d", text: "13", isCorrect: false }
            ],
            explanation: "When counting: 14, 15, 16, 17. The number 16 comes after 15.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g1w2q2",
            type: "fill-in-blank",
            question: "Complete the pattern: 11, 12, 13, __, 15",
            correctAnswer: 14,
            explanation: "The pattern increases by 1 each time: 11, 12, 13, 14, 15.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g1w2q3",
            type: "multiple-choice",
            question: "Which number is between 17 and 19?",
            options: [
              { id: "a", text: "16", isCorrect: false },
              { id: "b", text: "18", isCorrect: true },
              { id: "c", text: "20", isCorrect: false },
              { id: "d", text: "15", isCorrect: false }
            ],
            explanation: "The sequence is 17, 18, 19. The number 18 is between 17 and 19.",
            difficulty: "medium",
            points: 15
          },
          {
            id: "g1w2q4",
            type: "true-false",
            question: "The number 12 comes before the number 11.",
            correctAnswer: "false",
            explanation: "No, 11 comes before 12 in the counting sequence.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g1w2q5",
            type: "multiple-choice",
            question: "How many objects are shown? 🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟",
            options: [
              { id: "a", text: "16", isCorrect: false },
              { id: "b", text: "17", isCorrect: true },
              { id: "c", text: "18", isCorrect: false },
              { id: "d", text: "15", isCorrect: false }
            ],
            explanation: "Count each star carefully. There are 17 stars in total.",
            difficulty: "medium",
            points: 15
          },
          {
            id: "g1w2q6",
            type: "fill-in-blank",
            question: "What number comes before 20?",
            correctAnswer: 19,
            explanation: "The sequence is 18, 19, 20. The number 19 comes before 20.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g1w2q7",
            type: "multiple-choice",
            question: "Which is the largest number?",
            options: [
              { id: "a", text: "13", isCorrect: false },
              { id: "b", text: "18", isCorrect: true },
              { id: "c", text: "11", isCorrect: false },
              { id: "d", text: "16", isCorrect: false }
            ],
            explanation: "When we count up, 18 comes after all the other numbers, so it's the largest.",
            difficulty: "medium",
            points: 15
          },
          {
            id: "g1w2q8",
            type: "drag-drop",
            question: "Put these numbers in order from smallest to largest: 19, 12, 16, 14",
            correctAnswer: "12, 14, 16, 19",
            explanation: "From smallest to largest: 12, 14, 16, 19",
            difficulty: "hard",
            points: 20
          }
        ]
      }
    ]
  },
  {
    grade: 2,
    title: "Grade 2 Mathematics Quizzes",
    totalWeeks: 40,
    quizzes: [
      {
        grade: 2,
        week: 1,
        title: "Numbers to 100 Quiz",
        description: "Test your understanding of place value and counting to 100",
        totalQuestions: 10,
        timeLimit: 20,
        passingScore: 75,
        questions: [
          {
            id: "g2w1q1",
            type: "multiple-choice",
            question: "In the number 47, what digit is in the tens place?",
            options: [
              { id: "a", text: "7", isCorrect: false },
              { id: "b", text: "4", isCorrect: true },
              { id: "c", text: "47", isCorrect: false },
              { id: "d", text: "74", isCorrect: false }
            ],
            explanation: "In 47, the 4 is in the tens place and the 7 is in the ones place.",
            difficulty: "medium",
            points: 15
          },
          {
            id: "g2w1q2",
            type: "fill-in-blank",
            question: "Count by 10s: 10, 20, 30, __, 50",
            correctAnswer: 40,
            explanation: "When counting by 10s, we add 10 each time: 30 + 10 = 40.",
            difficulty: "easy",
            points: 10
          },
          {
            id: "g2w1q3",
            type: "multiple-choice",
            question: "Which number is the same as 6 tens and 3 ones?",
            options: [
              { id: "a", text: "36", isCorrect: false },
              { id: "b", text: "63", isCorrect: true },
              { id: "c", text: "93", isCorrect: false },
              { id: "d", text: "39", isCorrect: false }
            ],
            explanation: "6 tens = 60, and 3 ones = 3. So 60 + 3 = 63.",
            difficulty: "medium",
            points: 15
          }
        ]
      }
    ]
  }
];

// Helper functions for quiz management
export const getGradeQuizzes = (grade: number): GradeQuizzes | undefined => {
  return mathQuizzes.find(gradeQuiz => gradeQuiz.grade === grade);
};

export const getWeeklyQuiz = (grade: number, week: number): WeeklyQuiz | undefined => {
  const gradeQuizzes = getGradeQuizzes(grade);
  return gradeQuizzes?.quizzes.find(quiz => quiz.week === week);
};

export const getQuizQuestion = (grade: number, week: number, questionId: string): QuizQuestion | undefined => {
  const weeklyQuiz = getWeeklyQuiz(grade, week);
  return weeklyQuiz?.questions.find(question => question.id === questionId);
};

export const calculateQuizScore = (answers: Record<string, any>, quiz: WeeklyQuiz): number => {
  let totalPoints = 0;
  let earnedPoints = 0;

  quiz.questions.forEach(question => {
    totalPoints += question.points;
    const userAnswer = answers[question.id];
    
    if (question.type === 'multiple-choice') {
      const correctOption = question.options?.find(opt => opt.isCorrect);
      if (userAnswer === correctOption?.id) {
        earnedPoints += question.points;
      }
    } else if (question.type === 'fill-in-blank' || question.type === 'true-false') {
      if (userAnswer === question.correctAnswer) {
        earnedPoints += question.points;
      }
    }
  });

  return Math.round((earnedPoints / totalPoints) * 100);
};
