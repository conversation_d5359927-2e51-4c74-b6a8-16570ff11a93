// Sample Data Generator for MathQuest
// Creates realistic sample data for testing and demonstration

export interface SampleUser {
  id: string;
  name: string;
  grade: number;
  avatar: string;
  totalScore: number;
  completedWeeks: number;
  currentStreak: number;
  achievements: string[];
  timeSpent: number; // in minutes
}

export interface SampleProgress {
  userId: string;
  grade: number;
  week: number;
  topicId: string;
  completed: boolean;
  score: number;
  timeSpent: number;
  attempts: number;
  lastAttemptDate: Date;
}

// Sample users for demonstration
export const sampleUsers: SampleUser[] = [
  {
    id: "user1",
    name: "<PERSON>",
    grade: 1,
    avatar: "👧",
    totalScore: 1250,
    completedWeeks: 8,
    currentStreak: 5,
    achievements: ["first_lesson", "week_warrior", "perfect_score", "daily_learner"],
    timeSpent: 240
  },
  {
    id: "user2",
    name: "<PERSON>",
    grade: 2,
    avatar: "👦",
    totalScore: 2100,
    completedWeeks: 12,
    currentStreak: 7,
    achievements: ["first_lesson", "week_warrior", "month_master", "high_achiever", "week_streak"],
    timeSpent: 380
  },
  {
    id: "user3",
    name: "<PERSON>",
    grade: 3,
    avatar: "👧",
    totalScore: 3200,
    completedWeeks: 15,
    currentStreak: 12,
    achievements: ["first_lesson", "week_warrior", "month_master", "perfectionist", "counting_master", "addition_ace"],
    timeSpent: 520
  },
  {
    id: "user4",
    name: "Noah Wilson",
    grade: 4,
    avatar: "👦",
    totalScore: 4500,
    completedWeeks: 20,
    currentStreak: 15,
    achievements: ["first_lesson", "week_warrior", "month_master", "grade_champion", "multiplication_master"],
    timeSpent: 680
  },
  {
    id: "user5",
    name: "Olivia Brown",
    grade: 5,
    avatar: "👧",
    totalScore: 5800,
    completedWeeks: 25,
    currentStreak: 20,
    achievements: ["first_lesson", "week_warrior", "month_master", "grade_champion", "unstoppable"],
    timeSpent: 850
  }
];

// Generate sample progress data
export const generateSampleProgress = (): SampleProgress[] => {
  const progressData: SampleProgress[] = [];
  
  sampleUsers.forEach(user => {
    // Generate progress for completed weeks
    for (let week = 1; week <= user.completedWeeks; week++) {
      const topicId = `grade${user.grade}_week${week}`;
      const baseScore = 70 + Math.random() * 30; // 70-100
      const timeSpent = 15 + Math.random() * 25; // 15-40 minutes
      const attempts = Math.random() < 0.8 ? 1 : Math.floor(Math.random() * 3) + 1; // Most complete in 1 attempt
      
      progressData.push({
        userId: user.id,
        grade: user.grade,
        week: week,
        topicId: topicId,
        completed: true,
        score: Math.round(baseScore),
        timeSpent: Math.round(timeSpent),
        attempts: attempts,
        lastAttemptDate: new Date(Date.now() - (user.completedWeeks - week) * 7 * 24 * 60 * 60 * 1000)
      });
    }
    
    // Generate some incomplete progress for current week
    if (user.completedWeeks < 40) {
      const currentWeek = user.completedWeeks + 1;
      const topicId = `grade${user.grade}_week${currentWeek}`;
      
      progressData.push({
        userId: user.id,
        grade: user.grade,
        week: currentWeek,
        topicId: topicId,
        completed: false,
        score: Math.round(40 + Math.random() * 30), // Partial score
        timeSpent: Math.round(5 + Math.random() * 15),
        attempts: 1,
        lastAttemptDate: new Date()
      });
    }
  });
  
  return progressData;
};

// Leaderboard data
export interface LeaderboardEntry {
  rank: number;
  userId: string;
  name: string;
  grade: number;
  totalScore: number;
  avatar: string;
  achievements: number;
}

export const generateLeaderboard = (): LeaderboardEntry[] => {
  return sampleUsers
    .sort((a, b) => b.totalScore - a.totalScore)
    .map((user, index) => ({
      rank: index + 1,
      userId: user.id,
      name: user.name,
      grade: user.grade,
      totalScore: user.totalScore,
      avatar: user.avatar,
      achievements: user.achievements.length
    }));
};

// Weekly challenge data
export interface WeeklyChallenge {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  targetGrades: number[];
  goal: {
    type: 'score' | 'completion' | 'streak' | 'time';
    target: number;
    description: string;
  };
  rewards: {
    points: number;
    badge: string;
    title: string;
  };
  participants: number;
  topPerformers: {
    userId: string;
    name: string;
    achievement: number;
  }[];
}

export const currentWeeklyChallenge: WeeklyChallenge = {
  id: "challenge_week_1",
  title: "Math Marathon",
  description: "Complete 5 lessons this week to earn special rewards!",
  startDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
  endDate: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000), // 4 days from now
  targetGrades: [1, 2, 3, 4, 5],
  goal: {
    type: 'completion',
    target: 5,
    description: 'Complete 5 lessons'
  },
  rewards: {
    points: 100,
    badge: '🏃‍♂️',
    title: 'Marathon Runner'
  },
  participants: 1247,
  topPerformers: [
    { userId: "user3", name: "Sophia Davis", achievement: 8 },
    { userId: "user4", name: "Noah Wilson", achievement: 7 },
    { userId: "user2", name: "Liam Smith", achievement: 6 },
    { userId: "user5", name: "Olivia Brown", achievement: 5 },
    { userId: "user1", name: "Emma Johnson", achievement: 5 }
  ]
};

// Study statistics
export interface StudyStats {
  totalStudents: number;
  totalLessonsCompleted: number;
  averageScore: number;
  totalTimeSpent: number; // in hours
  mostPopularGrade: number;
  topAchievement: string;
  dailyActiveUsers: number;
  weeklyActiveUsers: number;
}

export const studyStatistics: StudyStats = {
  totalStudents: 15420,
  totalLessonsCompleted: 89650,
  averageScore: 84.2,
  totalTimeSpent: 12580, // hours
  mostPopularGrade: 3,
  topAchievement: "week_warrior",
  dailyActiveUsers: 3240,
  weeklyActiveUsers: 8950
};

// Recent activity feed
export interface ActivityItem {
  id: string;
  type: 'achievement' | 'completion' | 'high_score' | 'streak';
  userId: string;
  userName: string;
  description: string;
  timestamp: Date;
  icon: string;
  grade?: number;
  week?: number;
  score?: number;
}

export const recentActivity: ActivityItem[] = [
  {
    id: "activity1",
    type: "achievement",
    userId: "user3",
    userName: "Sophia Davis",
    description: "earned the Multiplication Master achievement!",
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    icon: "🏆",
    grade: 3
  },
  {
    id: "activity2",
    type: "high_score",
    userId: "user2",
    userName: "Liam Smith",
    description: "scored 98% on Place Value quiz!",
    timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
    icon: "⭐",
    grade: 2,
    week: 1,
    score: 98
  },
  {
    id: "activity3",
    type: "completion",
    userId: "user4",
    userName: "Noah Wilson",
    description: "completed Week 20 - Fractions and Decimals!",
    timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    icon: "✅",
    grade: 4,
    week: 20
  },
  {
    id: "activity4",
    type: "streak",
    userId: "user5",
    userName: "Olivia Brown",
    description: "reached a 20-day learning streak!",
    timestamp: new Date(Date.now() - 90 * 60 * 1000), // 1.5 hours ago
    icon: "🔥"
  },
  {
    id: "activity5",
    type: "achievement",
    userId: "user1",
    userName: "Emma Johnson",
    description: "earned the Perfect Score achievement!",
    timestamp: new Date(Date.now() - 120 * 60 * 1000), // 2 hours ago
    icon: "💯",
    grade: 1
  }
];

// Helper functions
export const getUserById = (userId: string): SampleUser | undefined => {
  return sampleUsers.find(user => user.id === userId);
};

export const getUsersByGrade = (grade: number): SampleUser[] => {
  return sampleUsers.filter(user => user.grade === grade);
};

export const getTopPerformers = (limit: number = 5): SampleUser[] => {
  return sampleUsers
    .sort((a, b) => b.totalScore - a.totalScore)
    .slice(0, limit);
};

export const getRecentActivityByUser = (userId: string): ActivityItem[] => {
  return recentActivity.filter(activity => activity.userId === userId);
};
