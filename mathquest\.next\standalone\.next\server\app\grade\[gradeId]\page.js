(()=>{var e={};e.id=161,e.ids=[161],e.modules={440:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});var s=i(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1130:(e,t,i)=>{"use strict";i.d(t,{Zp:()=>a});var s=i(687),r=i(3210),n=i(4780);let a=(0,r.forwardRef)(({className:e,...t},i)=>(0,s.jsx)("div",{ref:i,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));a.displayName="Card",(0,r.forwardRef)(({className:e,...t},i)=>(0,s.jsx)("div",{ref:i,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",(0,r.forwardRef)(({className:e,...t},i)=>(0,s.jsx)("h3",{ref:i,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",(0,r.forwardRef)(({className:e,...t},i)=>(0,s.jsx)("p",{ref:i,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",(0,r.forwardRef)(({className:e,...t},i)=>(0,s.jsx)("div",{ref:i,className:(0,n.cn)("p-6 pt-0",e),...t})).displayName="CardContent",(0,r.forwardRef)(({className:e,...t},i)=>(0,s.jsx)("div",{ref:i,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},1135:()=>{},2046:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>l,routeModule:()=>u,tree:()=>c});var s=i(5239),r=i(8088),n=i(8170),a=i.n(n),o=i(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);i.d(t,d);let c={children:["",{children:["grade",{children:["[gradeId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,6363)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\grade\\[gradeId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\grade\\[gradeId]\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/grade/[gradeId]/page",pathname:"/grade/[gradeId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2318:(e,t,i)=>{Promise.resolve().then(i.bind(i,7552))},2582:(e,t,i)=>{Promise.resolve().then(i.bind(i,3622))},2643:(e,t,i)=>{"use strict";i.d(t,{$:()=>a});var s=i(687),r=i(3210),n=i(4780);let a=(0,r.forwardRef)(({className:e,variant:t="default",size:i="default",...r},a)=>(0,s.jsx)("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===t,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===t,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===t,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"hover:bg-accent hover:text-accent-foreground":"ghost"===t,"text-primary underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===i,"h-9 rounded-md px-3":"sm"===i,"h-11 rounded-md px-8":"lg"===i,"h-10 w-10":"icon"===i},e),ref:a,...r}));a.displayName="Button"},2705:(e,t,i)=>{Promise.resolve().then(i.bind(i,6363))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3622:(e,t,i)=>{"use strict";i.d(t,{Providers:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\components\\Providers.tsx","Providers")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>c,metadata:()=>o,viewport:()=>d});var s=i(7413),r=i(5041),n=i.n(r);i(1135);var a=i(3622);let o={title:"MathQuest - Gamified Mathematics Learning",description:"Interactive mathematics learning platform for grades 1-10 with games, achievements, and personalized progress tracking",keywords:"mathematics, learning, education, games, kids, math, interactive",authors:[{name:"MathQuest Team"}]},d={width:"device-width",initialScale:1};function c({children:e}){return(0,s.jsx)("html",{lang:"en",className:"h-full",children:(0,s.jsx)("body",{className:`${n().className} h-full bg-gradient-to-br from-blue-50 to-purple-50 antialiased`,children:(0,s.jsx)(a.Providers,{children:e})})})}},4472:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>w});var s=i(687),r=i(3210),n=i(5814),a=i.n(n),o=i(5773),d=i(8559),c=i(2688);let l=(0,c.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var m=i(7268),u=i(8730),p=i(4398);let g=(0,c.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var f=i(2643),h=i(1130),b=i(6001);let x=[{grade:1,title:"Grade 1 Mathematics - Foundation Building",description:"Introduction to numbers, basic counting, shapes, and simple addition/subtraction",totalWeeks:40,weeks:[{week:1,title:"Numbers 1-10",description:"Introduction to counting and recognizing numbers 1-10",topics:["Counting objects","Number recognition","Number writing","One-to-one correspondence"],learningOutcomes:[{id:"g1w1_1",description:"Count objects up to 10",difficulty:"easy"},{id:"g1w1_2",description:"Recognize and write numbers 1-10",difficulty:"easy"},{id:"g1w1_3",description:"Match numbers to quantities",difficulty:"easy"}],gameTypes:["counting-game","number-matching","drag-drop"],estimatedHours:4},{week:2,title:"Numbers 11-20",description:"Extending counting skills to numbers 11-20",topics:["Counting 11-20","Teen numbers","Number patterns","Before and after"],learningOutcomes:[{id:"g1w2_1",description:"Count objects up to 20",difficulty:"easy"},{id:"g1w2_2",description:"Identify numbers that come before and after",difficulty:"medium"},{id:"g1w2_3",description:"Recognize patterns in teen numbers",difficulty:"medium"}],gameTypes:["number-sequence","pattern-recognition","counting-game"],estimatedHours:4},{week:3,title:"Basic Shapes",description:"Introduction to 2D shapes and their properties",topics:["Circle","Square","Triangle","Rectangle","Shape recognition"],learningOutcomes:[{id:"g1w3_1",description:"Identify basic 2D shapes",difficulty:"easy"},{id:"g1w3_2",description:"Describe shape properties",difficulty:"medium"},{id:"g1w3_3",description:"Find shapes in the environment",difficulty:"easy"}],gameTypes:["shape-matching","shape-hunt","drawing-game"],estimatedHours:4},{week:4,title:"Comparing Numbers",description:"Learning to compare quantities and numbers",topics:["More than","Less than","Equal to","Comparing groups","Number comparison"],learningOutcomes:[{id:"g1w4_1",description:"Compare two groups of objects",difficulty:"easy"},{id:"g1w4_2",description:"Use terms more, less, equal",difficulty:"easy"},{id:"g1w4_3",description:"Compare numbers 1-20",difficulty:"medium"}],gameTypes:["comparison-game","balance-scale","sorting-game"],estimatedHours:4},{week:5,title:"Addition Introduction",description:"Basic addition concepts with objects and pictures",topics:["Adding objects","Addition stories","Plus sign","Sum","Addition facts to 5"],learningOutcomes:[{id:"g1w5_1",description:"Add two groups of objects",difficulty:"easy"},{id:"g1w5_2",description:"Write simple addition sentences",difficulty:"medium"},{id:"g1w5_3",description:"Solve addition problems to 5",difficulty:"easy"}],gameTypes:["addition-blocks","story-problems","number-bonds"],estimatedHours:5},{week:6,title:"Subtraction Introduction",description:"Basic subtraction concepts with objects and pictures",topics:["Taking away objects","Subtraction stories","Minus sign","Difference","Subtraction facts to 5"],learningOutcomes:[{id:"g1w6_1",description:"Subtract objects from a group",difficulty:"easy"},{id:"g1w6_2",description:"Write simple subtraction sentences",difficulty:"medium"},{id:"g1w6_3",description:"Solve subtraction problems to 5",difficulty:"easy"}],gameTypes:["subtraction-blocks","story-problems","number-bonds"],estimatedHours:5},{week:7,title:"Addition and Subtraction to 10",description:"Extending addition and subtraction skills to 10",topics:["Addition facts to 10","Subtraction facts to 10","Fact families","Missing numbers"],learningOutcomes:[{id:"g1w7_1",description:"Add and subtract within 10 fluently",difficulty:"medium"},{id:"g1w7_2",description:"Understand fact families",difficulty:"medium"},{id:"g1w7_3",description:"Find missing numbers in equations",difficulty:"hard"}],gameTypes:["fact-family-house","missing-number","speed-math"],estimatedHours:6},{week:8,title:"Measurement - Length",description:"Introduction to measuring length using non-standard units",topics:["Comparing lengths","Measuring with objects","Longer/shorter","Units of measurement"],learningOutcomes:[{id:"g1w8_1",description:"Compare lengths of objects",difficulty:"easy"},{id:"g1w8_2",description:"Measure using non-standard units",difficulty:"medium"},{id:"g1w8_3",description:"Order objects by length",difficulty:"medium"}],gameTypes:["measurement-game","comparison-tool","ordering-activity"],estimatedHours:4},{week:9,title:"Time - Introduction",description:"Learning about time concepts and reading clocks",topics:["Day and night","Days of week","Months","Clock reading","Hour and minute"],learningOutcomes:[{id:"g1w9_1",description:"Identify day and night activities",difficulty:"easy"},{id:"g1w9_2",description:"Name days of the week in order",difficulty:"easy"},{id:"g1w9_3",description:"Read time to the hour",difficulty:"medium"}],gameTypes:["time-matching","clock-reading","sequence-game"],estimatedHours:4},{week:10,title:"Money - Introduction",description:"Recognizing coins and their values",topics:["Penny","Nickel","Dime","Quarter","Coin recognition","Counting money"],learningOutcomes:[{id:"g1w10_1",description:"Identify different coins",difficulty:"easy"},{id:"g1w10_2",description:"Know coin values",difficulty:"medium"},{id:"g1w10_3",description:"Count simple coin combinations",difficulty:"hard"}],gameTypes:["coin-matching","money-counting","shopping-game"],estimatedHours:5}]},{grade:2,title:"Grade 2 Mathematics - Building Fluency",description:"Strengthening number sense, place value, and basic operations",totalWeeks:40,weeks:[{week:1,title:"Numbers to 100",description:"Understanding place value and counting to 100",topics:["Counting to 100","Place value","Tens and ones","Number patterns"],learningOutcomes:[{id:"g2w1_1",description:"Count by 1s, 2s, 5s, and 10s to 100",difficulty:"easy"},{id:"g2w1_2",description:"Understand place value in 2-digit numbers",difficulty:"medium"},{id:"g2w1_3",description:"Identify patterns in number sequences",difficulty:"medium"}],gameTypes:["place-value-blocks","number-patterns","counting-by"],estimatedHours:5},{week:2,title:"Addition with Regrouping",description:"Two-digit addition with and without regrouping",topics:["Adding 2-digit numbers","Regrouping","Mental math strategies","Estimation"],learningOutcomes:[{id:"g2w2_1",description:"Add 2-digit numbers without regrouping",difficulty:"medium"},{id:"g2w2_2",description:"Add 2-digit numbers with regrouping",difficulty:"hard"},{id:"g2w2_3",description:"Estimate sums",difficulty:"medium"}],gameTypes:["base-ten-blocks","mental-math","estimation-game"],estimatedHours:6},{week:3,title:"Subtraction with Regrouping",description:"Two-digit subtraction with and without regrouping",topics:["Subtracting 2-digit numbers","Borrowing","Mental subtraction","Word problems"],learningOutcomes:[{id:"g2w3_1",description:"Subtract 2-digit numbers without regrouping",difficulty:"medium"},{id:"g2w3_2",description:"Subtract 2-digit numbers with regrouping",difficulty:"hard"},{id:"g2w3_3",description:"Solve subtraction word problems",difficulty:"hard"}],gameTypes:["base-ten-blocks","borrowing-game","word-problems"],estimatedHours:6},{week:4,title:"Geometry - 2D Shapes",description:"Advanced study of 2D shapes and their properties",topics:["Polygons","Sides and vertices","Symmetry","Shape patterns"],learningOutcomes:[{id:"g2w4_1",description:"Identify polygons by number of sides",difficulty:"medium"},{id:"g2w4_2",description:"Find lines of symmetry",difficulty:"hard"},{id:"g2w4_3",description:"Create and extend shape patterns",difficulty:"medium"}],gameTypes:["shape-sorting","symmetry-game","pattern-builder"],estimatedHours:5},{week:5,title:"Measurement - Standard Units",description:"Introduction to standard units of measurement",topics:["Inches","Feet","Centimeters","Meters","Measuring tools"],learningOutcomes:[{id:"g2w5_1",description:"Measure objects using inches and feet",difficulty:"medium"},{id:"g2w5_2",description:"Measure objects using centimeters",difficulty:"medium"},{id:"g2w5_3",description:"Choose appropriate measuring tools",difficulty:"medium"}],gameTypes:["measurement-practice","unit-conversion","tool-selection"],estimatedHours:5}]}],y=e=>x.find(t=>t.grade===e);function w(){let e=parseInt((0,o.useParams)().gradeId),[t,i]=(0,r.useState)(null),[n,c]=(0,r.useState)(1),x=y(e),w=e=>t?.topics.find(t=>t.topicId===e)||null,v=(s,r,n)=>{if(!t)return;let a=t.topics.findIndex(e=>e.topicId===s),o={topicId:s,completed:r>=70,score:Math.max(r,w(s)?.score||0),timeSpent:(w(s)?.timeSpent||0)+n,attempts:(w(s)?.attempts||0)+1},d=[...t.topics];a>=0?d[a]=o:d.push(o);let c={...t,topics:d,totalScore:d.reduce((e,t)=>e+t.score,0),totalTimeSpent:d.reduce((e,t)=>e+t.timeSpent,0)};i(c),localStorage.setItem(`mathquest-grade-${e}`,JSON.stringify(c));let l=JSON.parse(localStorage.getItem("mathquest-progress")||"[]"),m=l.findIndex(t=>t.grade===e);m>=0&&(l[m]={...l[m],completedTopics:d.filter(e=>e.completed).length,totalScore:c.totalScore,timeSpent:c.totalTimeSpent},localStorage.setItem("mathquest-progress",JSON.stringify(l)))};if(!x)return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)(h.Zp,{className:"p-8 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Grade Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"The requested grade level is not available."}),(0,s.jsx)(a(),{href:"/grades",children:(0,s.jsx)(f.$,{children:"Back to Grades"})})]})});let j=t?.topics.filter(e=>e.completed).length||0,N=Math.round(j/x.totalWeeks*100);return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)(a(),{href:"/grades",children:(0,s.jsxs)(f.$,{variant:"ghost",className:"mb-4",children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Back to Grades"]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:x.title}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:x.description})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[N,"%"]}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Complete"})]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-4",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full transition-all duration-500",style:{width:`${N}%`}})}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,s.jsx)(l,{className:"h-5 w-5 text-green-500 mr-1"}),(0,s.jsx)("span",{className:"font-semibold",children:j})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Topics Completed"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 text-yellow-500 mr-1"}),(0,s.jsx)("span",{className:"font-semibold",children:t?.totalScore||0})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Total Score"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-blue-500 mr-1"}),(0,s.jsx)("span",{className:"font-semibold",children:Math.round((t?.totalTimeSpent||0)/60)})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Hours Spent"})]})]})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"Select a Week to Study"}),(0,s.jsx)("div",{className:"grid grid-cols-5 md:grid-cols-10 gap-2",children:Array.from({length:x.totalWeeks},(e,t)=>t+1).map(t=>{let i=x.weeks.find(e=>e.week===t)?w(`grade${e}_week${t}`):null,r=i?.completed||!1,a=n===t;return(0,s.jsxs)(f.$,{variant:a?"default":"outline",size:"sm",onClick:()=>c(t),className:`relative ${r?"bg-green-100 border-green-300":""}`,children:[t,r&&(0,s.jsx)(l,{className:"h-3 w-3 text-green-600 absolute -top-1 -right-1"})]},t)})})]}),x.weeks.find(e=>e.week===n)&&(0,s.jsxs)(b.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"grid md:grid-cols-2 gap-6",children:[(0,s.jsxs)(h.Zp,{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:["Week ",n,": ",x.weeks.find(e=>e.week===n)?.title]}),(0,s.jsx)("p",{className:"text-gray-600",children:x.weeks.find(e=>e.week===n)?.description})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-700 mb-2",children:"Topics Covered:"}),(0,s.jsx)("ul",{className:"space-y-1",children:x.weeks.find(e=>e.week===n)?.topics.map((e,t)=>(0,s.jsxs)("li",{className:"text-sm text-gray-600 flex items-center",children:[(0,s.jsx)("div",{className:"w-1 h-1 bg-purple-400 rounded-full mr-2"}),e]},t))})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-700 mb-2",children:"Learning Outcomes:"}),(0,s.jsx)("ul",{className:"space-y-1",children:x.weeks.find(e=>e.week===n)?.learningOutcomes.map((e,t)=>(0,s.jsxs)("li",{className:"text-sm text-gray-600 flex items-center",children:[(0,s.jsx)(p.A,{className:`h-3 w-3 mr-2 ${"easy"===e.difficulty?"text-green-500":"medium"===e.difficulty?"text-yellow-500":"text-red-500"}`}),e.description]},t))})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsx)("strong",{children:"Estimated Time:"})," ",x.weeks.find(e=>e.week===n)?.estimatedHours," hours"]})]}),(0,s.jsxs)(h.Zp,{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"Practice Games"}),(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsx)(a(),{href:"/demo",children:(0,s.jsxs)(f.$,{variant:"outline",className:"w-full justify-start",children:[(0,s.jsx)(g,{className:"mr-2 h-4 w-4"}),"Interactive Demo Games"]})}),(0,s.jsxs)(f.$,{className:"w-full justify-start",onClick:()=>{v(`grade${e}_week${n}`,Math.floor(30*Math.random())+70,Math.floor(10*Math.random())+5)},children:[(0,s.jsx)(g,{className:"mr-2 h-4 w-4"}),"Start Week ",n," Practice"]})]}),(()=>{let t=w(`grade${e}_week${n}`);return t?(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-700 mb-2",children:"Your Progress:"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Best Score:"}),(0,s.jsxs)("span",{className:"font-semibold",children:[t.score,"%"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Time Spent:"}),(0,s.jsxs)("span",{className:"font-semibold",children:[t.timeSpent," minutes"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Attempts:"}),(0,s.jsx)("span",{className:"font-semibold",children:t.attempts})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Status:"}),(0,s.jsx)("span",{className:`font-semibold ${t.completed?"text-green-600":"text-orange-600"}`,children:t.completed?"Completed":"In Progress"})]})]})]}):null})()]})]},n)]})})}},4761:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,5543,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},4780:(e,t,i)=>{"use strict";i.d(t,{cn:()=>n});var s=i(9384),r=i(2348);function n(...e){return(0,r.QP)((0,s.$)(e))}},6363:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\augument\\\\onstud\\\\mathquest\\\\src\\\\app\\\\grade\\\\[gradeId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\grade\\[gradeId]\\page.tsx","default")},7552:(e,t,i)=>{"use strict";i.d(t,{Providers:()=>n});var s=i(687),r=i(2136);function n({children:e}){return(0,s.jsx)(r.SessionProvider,{children:e})}},7785:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},7857:(e,t,i)=>{Promise.resolve().then(i.bind(i,4472))},8730:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[447,70,567,135],()=>i(2046));module.exports=s})();