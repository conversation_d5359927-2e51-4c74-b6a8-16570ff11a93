// Comprehensive Achievement System for MathQuest
// Achievements organized by category and difficulty

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'completion' | 'streak' | 'score' | 'time' | 'special' | 'mastery';
  points: number;
  requirements: {
    type: string;
    value: number;
    grade?: number;
    week?: number;
    subject?: string;
  };
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlockedMessage: string;
  prerequisite?: string; // ID of required achievement
}

export const achievements: Achievement[] = [
  // Completion Achievements
  {
    id: 'first_lesson',
    name: 'First Steps',
    description: 'Complete your very first lesson',
    icon: '🎯',
    category: 'completion',
    points: 10,
    requirements: { type: 'lessons_completed', value: 1 },
    rarity: 'common',
    unlockedMessage: 'Congratulations! You\'ve taken your first step in your math journey!'
  },
  {
    id: 'week_warrior',
    name: 'Week Warrior',
    description: 'Complete all lessons in a week',
    icon: '⚔️',
    category: 'completion',
    points: 50,
    requirements: { type: 'week_completed', value: 1 },
    rarity: 'rare',
    unlockedMessage: 'Amazing! You\'ve conquered an entire week of math!'
  },
  {
    id: 'month_master',
    name: 'Month Master',
    description: 'Complete 4 weeks of lessons',
    icon: '👑',
    category: 'completion',
    points: 200,
    requirements: { type: 'weeks_completed', value: 4 },
    rarity: 'epic',
    unlockedMessage: 'Incredible! You\'ve mastered a full month of mathematics!',
    prerequisite: 'week_warrior'
  },
  {
    id: 'grade_champion',
    name: 'Grade Champion',
    description: 'Complete an entire grade level',
    icon: '🏆',
    category: 'completion',
    points: 1000,
    requirements: { type: 'grade_completed', value: 1 },
    rarity: 'legendary',
    unlockedMessage: 'Outstanding! You\'ve completed an entire grade level!',
    prerequisite: 'month_master'
  },

  // Score Achievements
  {
    id: 'perfect_score',
    name: 'Perfect Score',
    description: 'Get 100% on any quiz',
    icon: '💯',
    category: 'score',
    points: 25,
    requirements: { type: 'perfect_score', value: 100 },
    rarity: 'rare',
    unlockedMessage: 'Perfect! You got every question right!'
  },
  {
    id: 'high_achiever',
    name: 'High Achiever',
    description: 'Score 90% or higher on 5 quizzes',
    icon: '⭐',
    category: 'score',
    points: 75,
    requirements: { type: 'high_scores', value: 5 },
    rarity: 'rare',
    unlockedMessage: 'Excellent work! You consistently score high on your quizzes!'
  },
  {
    id: 'perfectionist',
    name: 'Perfectionist',
    description: 'Get perfect scores on 10 quizzes',
    icon: '🌟',
    category: 'score',
    points: 250,
    requirements: { type: 'perfect_scores', value: 10 },
    rarity: 'epic',
    unlockedMessage: 'Wow! You\'re a true perfectionist!',
    prerequisite: 'perfect_score'
  },

  // Streak Achievements
  {
    id: 'daily_learner',
    name: 'Daily Learner',
    description: 'Complete lessons for 3 days in a row',
    icon: '📅',
    category: 'streak',
    points: 30,
    requirements: { type: 'daily_streak', value: 3 },
    rarity: 'common',
    unlockedMessage: 'Great habit! You\'re learning every day!'
  },
  {
    id: 'week_streak',
    name: 'Week Streak',
    description: 'Complete lessons for 7 days in a row',
    icon: '🔥',
    category: 'streak',
    points: 100,
    requirements: { type: 'daily_streak', value: 7 },
    rarity: 'rare',
    unlockedMessage: 'You\'re on fire! A full week of daily learning!',
    prerequisite: 'daily_learner'
  },
  {
    id: 'unstoppable',
    name: 'Unstoppable',
    description: 'Complete lessons for 30 days in a row',
    icon: '🚀',
    category: 'streak',
    points: 500,
    requirements: { type: 'daily_streak', value: 30 },
    rarity: 'legendary',
    unlockedMessage: 'Unstoppable! You\'ve built an amazing learning habit!',
    prerequisite: 'week_streak'
  },

  // Time Achievements
  {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Complete a quiz in under 5 minutes',
    icon: '⚡',
    category: 'time',
    points: 40,
    requirements: { type: 'quiz_time', value: 300 }, // 5 minutes in seconds
    rarity: 'rare',
    unlockedMessage: 'Lightning fast! You completed that quiz super quickly!'
  },
  {
    id: 'marathon_learner',
    name: 'Marathon Learner',
    description: 'Study for 60 minutes in one session',
    icon: '🏃',
    category: 'time',
    points: 80,
    requirements: { type: 'session_time', value: 3600 }, // 60 minutes in seconds
    rarity: 'epic',
    unlockedMessage: 'Impressive endurance! You studied for a full hour!'
  },

  // Special Achievements
  {
    id: 'early_bird',
    name: 'Early Bird',
    description: 'Complete a lesson before 8 AM',
    icon: '🌅',
    category: 'special',
    points: 20,
    requirements: { type: 'early_completion', value: 8 }, // 8 AM
    rarity: 'common',
    unlockedMessage: 'Early bird catches the worm! Great morning study session!'
  },
  {
    id: 'night_owl',
    name: 'Night Owl',
    description: 'Complete a lesson after 8 PM',
    icon: '🦉',
    category: 'special',
    points: 20,
    requirements: { type: 'late_completion', value: 20 }, // 8 PM (20:00)
    rarity: 'common',
    unlockedMessage: 'Night owl! You\'re dedicated to learning even in the evening!'
  },
  {
    id: 'weekend_warrior',
    name: 'Weekend Warrior',
    description: 'Complete lessons on both Saturday and Sunday',
    icon: '🏖️',
    category: 'special',
    points: 60,
    requirements: { type: 'weekend_completion', value: 2 },
    rarity: 'rare',
    unlockedMessage: 'Weekend warrior! You don\'t take breaks from learning!'
  },

  // Mastery Achievements (Grade-specific)
  {
    id: 'counting_master',
    name: 'Counting Master',
    description: 'Master all counting lessons in Grade 1',
    icon: '🔢',
    category: 'mastery',
    points: 150,
    requirements: { type: 'topic_mastery', value: 100, grade: 1, subject: 'counting' },
    rarity: 'epic',
    unlockedMessage: 'You\'ve mastered counting! Numbers are your friends!'
  },
  {
    id: 'addition_ace',
    name: 'Addition Ace',
    description: 'Master all addition lessons in Grade 1',
    icon: '➕',
    category: 'mastery',
    points: 150,
    requirements: { type: 'topic_mastery', value: 100, grade: 1, subject: 'addition' },
    rarity: 'epic',
    unlockedMessage: 'Addition ace! You can add numbers like a pro!'
  },
  {
    id: 'subtraction_star',
    name: 'Subtraction Star',
    description: 'Master all subtraction lessons in Grade 1',
    icon: '➖',
    category: 'mastery',
    points: 150,
    requirements: { type: 'topic_mastery', value: 100, grade: 1, subject: 'subtraction' },
    rarity: 'epic',
    unlockedMessage: 'Subtraction star! Taking away numbers is easy for you!'
  },
  {
    id: 'shape_detective',
    name: 'Shape Detective',
    description: 'Master all geometry lessons in Grade 1',
    icon: '🔍',
    category: 'mastery',
    points: 150,
    requirements: { type: 'topic_mastery', value: 100, grade: 1, subject: 'geometry' },
    rarity: 'epic',
    unlockedMessage: 'Shape detective! You can spot any shape anywhere!'
  },
  {
    id: 'place_value_pro',
    name: 'Place Value Pro',
    description: 'Master place value concepts in Grade 2',
    icon: '📊',
    category: 'mastery',
    points: 200,
    requirements: { type: 'topic_mastery', value: 100, grade: 2, subject: 'place_value' },
    rarity: 'epic',
    unlockedMessage: 'Place value pro! You understand tens and ones perfectly!'
  },
  {
    id: 'multiplication_master',
    name: 'Multiplication Master',
    description: 'Master multiplication tables 1-10',
    icon: '✖️',
    category: 'mastery',
    points: 300,
    requirements: { type: 'topic_mastery', value: 100, grade: 3, subject: 'multiplication' },
    rarity: 'legendary',
    unlockedMessage: 'Multiplication master! You know your times tables inside out!'
  },

  // Fun Achievements
  {
    id: 'mistake_maker',
    name: 'Learning from Mistakes',
    description: 'Get a question wrong and then get it right on retry',
    icon: '🎓',
    category: 'special',
    points: 15,
    requirements: { type: 'learn_from_mistake', value: 1 },
    rarity: 'common',
    unlockedMessage: 'Great job learning from your mistakes! That\'s how we grow!'
  },
  {
    id: 'helper',
    name: 'Helpful Friend',
    description: 'Help another student with a problem',
    icon: '🤝',
    category: 'special',
    points: 50,
    requirements: { type: 'help_others', value: 1 },
    rarity: 'rare',
    unlockedMessage: 'You\'re a helpful friend! Sharing knowledge makes everyone smarter!'
  },
  {
    id: 'explorer',
    name: 'Math Explorer',
    description: 'Try lessons from 3 different grade levels',
    icon: '🗺️',
    category: 'special',
    points: 100,
    requirements: { type: 'explore_grades', value: 3 },
    rarity: 'rare',
    unlockedMessage: 'Math explorer! You love discovering new mathematical concepts!'
  }
];

// Helper functions for achievement management
export const getAchievementById = (id: string): Achievement | undefined => {
  return achievements.find(achievement => achievement.id === id);
};

export const getAchievementsByCategory = (category: Achievement['category']): Achievement[] => {
  return achievements.filter(achievement => achievement.category === category);
};

export const getAchievementsByRarity = (rarity: Achievement['rarity']): Achievement[] => {
  return achievements.filter(achievement => achievement.rarity === rarity);
};

export const getAchievementsByGrade = (grade: number): Achievement[] => {
  return achievements.filter(achievement => 
    !achievement.requirements.grade || achievement.requirements.grade === grade
  );
};

export const checkAchievementUnlock = (
  achievement: Achievement, 
  userStats: Record<string, any>
): boolean => {
  const req = achievement.requirements;
  
  switch (req.type) {
    case 'lessons_completed':
      return userStats.lessonsCompleted >= req.value;
    case 'week_completed':
      return userStats.weeksCompleted >= req.value;
    case 'weeks_completed':
      return userStats.weeksCompleted >= req.value;
    case 'grade_completed':
      return userStats.gradesCompleted >= req.value;
    case 'perfect_score':
      return userStats.perfectScores >= 1;
    case 'perfect_scores':
      return userStats.perfectScores >= req.value;
    case 'high_scores':
      return userStats.highScores >= req.value;
    case 'daily_streak':
      return userStats.currentStreak >= req.value;
    case 'quiz_time':
      return userStats.fastestQuizTime <= req.value;
    case 'session_time':
      return userStats.longestSession >= req.value;
    default:
      return false;
  }
};
