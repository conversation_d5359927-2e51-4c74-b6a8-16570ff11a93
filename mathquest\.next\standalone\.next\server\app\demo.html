<!DOCTYPE html><html lang="en" class="h-full"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/40b5e0a264840ca9.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-75e90ec69d4ac2a5.js"/><script src="/_next/static/chunks/4bd1b696-1962bfe149af46cd.js" async=""></script><script src="/_next/static/chunks/684-ef3ff7dc6c26f8d6.js" async=""></script><script src="/_next/static/chunks/main-app-fba1cd3967d854bc.js" async=""></script><script src="/_next/static/chunks/723-da20890518dd223f.js" async=""></script><script src="/_next/static/chunks/app/layout-9a0011ea89ba329c.js" async=""></script><script src="/_next/static/chunks/874-6052627df6fde20c.js" async=""></script><script src="/_next/static/chunks/360-2c14fa0b4cb4ce3c.js" async=""></script><script src="/_next/static/chunks/app/demo/page-be53f87e7fef33c6.js" async=""></script><title>MathQuest - Gamified Mathematics Learning</title><meta name="description" content="Interactive mathematics learning platform for grades 1-10 with games, achievements, and personalized progress tracking"/><meta name="author" content="MathQuest Team"/><meta name="keywords" content="mathematics, learning, education, games, kids, math, interactive"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c h-full bg-gradient-to-br from-blue-50 to-purple-50 antialiased"><div class="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8"><div class="max-w-4xl mx-auto px-4"><div class="mb-8"><a href="/"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left mr-2 h-4 w-4" aria-hidden="true"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>Back to Home</button></a><h1 class="text-4xl font-bold text-center mb-4"><span class="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">MathQuest Interactive Demo</span></h1><p class="text-xl text-gray-600 text-center max-w-2xl mx-auto">Try our collection of interactive math games designed for different grade levels. Choose a game and start learning!</p></div><div class="mb-8"><div class="space-y-8"><div class="text-center"><div class="flex items-center justify-center mb-4"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open h-8 w-8 text-purple-600 mr-3" aria-hidden="true"><path d="M12 7v14"></path><path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path></svg><h1 class="text-3xl font-bold text-gray-800">Math Games</h1></div><p class="text-lg text-gray-600 mb-6">Choose a game to practice your math skills!</p></div><div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6"><div tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6 h-full cursor-pointer hover:shadow-lg transition-all relative overflow-hidden"><div class="bg-gradient-to-r from-blue-400 to-blue-600 p-4 rounded-lg mb-4 text-white w-fit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hash h-8 w-8" aria-hidden="true"><line x1="4" x2="20" y1="9" y2="9"></line><line x1="4" x2="20" y1="15" y2="15"></line><line x1="10" x2="8" y1="3" y2="21"></line><line x1="16" x2="14" y1="3" y2="21"></line></svg></div><div class="space-y-3"><div><h3 class="text-xl font-bold text-gray-800 mb-1">Counting Game</h3><p class="text-gray-600 text-sm">Count objects and learn numbers 1-10</p></div><div class="flex items-center justify-between"><span class="text-sm font-medium text-gray-500">Grade 1</span><span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Easy</span></div><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full mt-4">Start Game</button></div></div></div><div tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6 h-full cursor-pointer hover:shadow-lg transition-all relative overflow-hidden"><div class="bg-gradient-to-r from-green-400 to-green-600 p-4 rounded-lg mb-4 text-white w-fit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calculator h-8 w-8" aria-hidden="true"><rect width="16" height="20" x="4" y="2" rx="2"></rect><line x1="8" x2="16" y1="6" y2="6"></line><line x1="16" x2="16" y1="14" y2="18"></line><path d="M16 10h.01"></path><path d="M12 10h.01"></path><path d="M8 10h.01"></path><path d="M12 14h.01"></path><path d="M8 14h.01"></path><path d="M12 18h.01"></path><path d="M8 18h.01"></path></svg></div><div class="space-y-3"><div><h3 class="text-xl font-bold text-gray-800 mb-1">Addition Challenge</h3><p class="text-gray-600 text-sm">Practice addition with fun problems</p></div><div class="flex items-center justify-between"><span class="text-sm font-medium text-gray-500">Grade 1-2</span><span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Easy</span></div><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full mt-4">Start Game</button></div></div></div><div tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6 h-full cursor-pointer hover:shadow-lg transition-all relative overflow-hidden"><div class="bg-gradient-to-r from-purple-400 to-purple-600 p-4 rounded-lg mb-4 text-white w-fit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shapes h-8 w-8" aria-hidden="true"><path d="M8.3 10a.7.7 0 0 1-.626-1.079L11.4 3a.7.7 0 0 1 1.198-.043L16.3 8.9a.7.7 0 0 1-.572 1.1Z"></path><rect x="3" y="14" width="7" height="7" rx="1"></rect><circle cx="17.5" cy="17.5" r="3.5"></circle></svg></div><div class="space-y-3"><div><h3 class="text-xl font-bold text-gray-800 mb-1">Shape Matching</h3><p class="text-gray-600 text-sm">Identify and match different shapes</p></div><div class="flex items-center justify-between"><span class="text-sm font-medium text-gray-500">Grade 1</span><span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Easy</span></div><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full mt-4">Start Game</button></div></div></div><div tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6 h-full cursor-pointer hover:shadow-lg transition-all relative overflow-hidden"><div class="bg-gradient-to-r from-indigo-400 to-indigo-600 p-4 rounded-lg mb-4 text-white w-fit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right h-8 w-8" aria-hidden="true"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></div><div class="space-y-3"><div><h3 class="text-xl font-bold text-gray-800 mb-1">Number Patterns</h3><p class="text-gray-600 text-sm">Complete number sequences and patterns</p></div><div class="flex items-center justify-between"><span class="text-sm font-medium text-gray-500">Grade 2-3</span><span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Medium</span></div><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full mt-4">Start Game</button></div></div></div><div tabindex="0" style="opacity:0;transform:translateY(20px)"><div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6 h-full cursor-pointer hover:shadow-lg transition-all relative overflow-hidden"><div class="bg-gradient-to-r from-orange-400 to-orange-600 p-4 rounded-lg mb-4 text-white w-fit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-8 w-8" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></div><div class="space-y-3"><div><h3 class="text-xl font-bold text-gray-800 mb-1">Times Tables</h3><p class="text-gray-600 text-sm">Master multiplication with visual aids</p></div><div class="flex items-center justify-between"><span class="text-sm font-medium text-gray-500">Grade 3-4</span><span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Hard</span></div><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full mt-4">Start Game</button></div></div></div></div></div></div><div class="grid md:grid-cols-3 gap-6 mt-12"><div class="bg-white p-6 rounded-lg shadow-sm border"><div class="text-3xl mb-3">🎯</div><h3 class="text-lg font-semibold mb-2">Interactive Learning</h3><p class="text-gray-600">Engaging games that make learning math fun and memorable for children.</p></div><div class="bg-white p-6 rounded-lg shadow-sm border"><div class="text-3xl mb-3">📊</div><h3 class="text-lg font-semibold mb-2">Progress Tracking</h3><p class="text-gray-600">Real-time feedback and detailed progress reports for students and parents.</p></div><div class="bg-white p-6 rounded-lg shadow-sm border"><div class="text-3xl mb-3">🏆</div><h3 class="text-lg font-semibold mb-2">Achievement System</h3><p class="text-gray-600">Badges, points, and rewards that motivate students to keep learning.</p></div></div><div class="text-center mt-12"><h2 class="text-2xl font-bold mb-4">Ready to Start Learning?</h2><p class="text-gray-600 mb-6">Join thousands of students already improving their math skills with MathQuest.</p><div class="space-x-4"><a href="/register"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-11 rounded-md px-8">Get Started Free</button></a><a href="/"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 rounded-md px-8">Learn More</button></a></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-75e90ec69d4ac2a5.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[2926,[\"723\",\"static/chunks/723-da20890518dd223f.js\",\"177\",\"static/chunks/app/layout-9a0011ea89ba329c.js\"],\"Providers\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[894,[],\"ClientPageRoot\"]\n6:I[5050,[\"874\",\"static/chunks/874-6052627df6fde20c.js\",\"360\",\"static/chunks/360-2c14fa0b4cb4ce3c.js\",\"436\",\"static/chunks/app/demo/page-be53f87e7fef33c6.js\"],\"default\"]\n9:I[9665,[],\"MetadataBoundary\"]\nb:I[9665,[],\"OutletBoundary\"]\ne:I[4911,[],\"AsyncMetadataOutlet\"]\n10:I[9665,[],\"ViewportBoundary\"]\n12:I[6614,[],\"\"]\n:HL[\"/_next/static/css/40b5e0a264840ca9.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"0AFyMCfoM7HbuNDfv64Cw\",\"p\":\"\",\"c\":[\"\",\"demo\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"demo\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/40b5e0a264840ca9.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"h-full\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c h-full bg-gradient-to-br from-blue-50 to-purple-50 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"demo\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],[\"$\",\"$L9\",null,{\"children\":\"$La\"}],null,[\"$\",\"$Lb\",null,{\"children\":[\"$Lc\",\"$Ld\",[\"$\",\"$Le\",null,{\"promise\":\"$@f\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"p1mChZnjCEKej_uUpOmCT\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$12\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"13:\"$Sreact.suspense\"\n14:I[4911,[],\"AsyncMetadata\"]\n7:{}\n8:{}\na:[\"$\",\"$13\",null,{\"fallback\":null,\"children\":[\"$\",\"$L14\",null,{\"promise\":\"$@15\"}]}]\n"])</script><script>self.__next_f.push([1,"d:null\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nc:null\n"])</script><script>self.__next_f.push([1,"15:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"MathQuest - Gamified Mathematics Learning\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Interactive mathematics learning platform for grades 1-10 with games, achievements, and personalized progress tracking\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"MathQuest Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"mathematics, learning, education, games, kids, math, interactive\"}],[\"$\",\"link\",\"4\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\nf:{\"metadata\":\"$15:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>