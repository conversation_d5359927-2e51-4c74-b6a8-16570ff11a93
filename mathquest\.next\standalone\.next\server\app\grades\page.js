(()=>{var e={};e.id=163,e.ids=[163],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1130:(e,t,r)=>{"use strict";r.d(t,{Zp:()=>o});var s=r(687),i=r(3210),a=r(4780);let o=(0,i.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card",(0,i.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",(0,i.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",(0,i.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",(0,i.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t})).displayName="CardContent",(0,i.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},1135:()=>{},2080:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},2318:(e,t,r)=>{Promise.resolve().then(r.bind(r,7552))},2582:(e,t,r)=>{Promise.resolve().then(r.bind(r,3622))},2643:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var s=r(687),i=r(3210),a=r(4780);let o=(0,i.forwardRef)(({className:e,variant:t="default",size:r="default",...i},o)=>(0,s.jsx)("button",{className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===t,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===t,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===t,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"hover:bg-accent hover:text-accent-foreground":"ghost"===t,"text-primary underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===r,"h-9 rounded-md px-3":"sm"===r,"h-11 rounded-md px-8":"lg"===r,"h-10 w-10":"icon"===r},e),ref:o,...i}));o.displayName="Button"},2780:(e,t,r)=>{Promise.resolve().then(r.bind(r,7849))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3159:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\augument\\\\onstud\\\\mathquest\\\\src\\\\app\\\\grades\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\grades\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3622:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\components\\Providers.tsx","Providers")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>n,viewport:()=>l});var s=r(7413),i=r(5041),a=r.n(i);r(1135);var o=r(3622);let n={title:"MathQuest - Gamified Mathematics Learning",description:"Interactive mathematics learning platform for grades 1-10 with games, achievements, and personalized progress tracking",keywords:"mathematics, learning, education, games, kids, math, interactive",authors:[{name:"MathQuest Team"}]},l={width:"device-width",initialScale:1};function d({children:e}){return(0,s.jsx)("html",{lang:"en",className:"h-full",children:(0,s.jsx)("body",{className:`${a().className} h-full bg-gradient-to-br from-blue-50 to-purple-50 antialiased`,children:(0,s.jsx)(o.Providers,{children:e})})})}},4636:(e,t,r)=>{Promise.resolve().then(r.bind(r,3159))},4761:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,5543,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(9384),i=r(2348);function a(...e){return(0,i.QP)((0,s.$)(e))}},7552:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>a});var s=r(687),i=r(2136);function a({children:e}){return(0,s.jsx)(i.SessionProvider,{children:e})}},7785:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7849:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(687),i=r(5814),a=r.n(i),o=r(3210),n=r(8559),l=r(2080),d=r(7268),c=r(8730);let m=(0,r(2688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var u=r(4398),p=r(2643),x=r(1130),h=r(6001);let g={1:{title:"Foundation Building",topics:["Numbers 1-20","Basic Shapes","Simple Addition","Counting"],color:"from-blue-400 to-blue-600"},2:{title:"Building Fluency",topics:["Place Value","Addition/Subtraction","Measurement","Time"],color:"from-green-400 to-green-600"},3:{title:"Expanding Skills",topics:["Multiplication","Division","Fractions","Geometry"],color:"from-purple-400 to-purple-600"},4:{title:"Strengthening Concepts",topics:["Multi-digit Operations","Decimals","Area & Perimeter","Data"],color:"from-orange-400 to-orange-600"},5:{title:"Advanced Operations",topics:["Fractions & Decimals","Volume","Coordinate Plane","Patterns"],color:"from-red-400 to-red-600"},6:{title:"Pre-Algebra Prep",topics:["Ratios","Percentages","Integers","Expressions"],color:"from-pink-400 to-pink-600"},7:{title:"Algebraic Thinking",topics:["Equations","Proportions","Geometry","Probability"],color:"from-indigo-400 to-indigo-600"},8:{title:"Advanced Algebra",topics:["Linear Functions","Systems","Transformations","Statistics"],color:"from-teal-400 to-teal-600"},9:{title:"High School Prep",topics:["Quadratics","Exponentials","Trigonometry","Polynomials"],color:"from-cyan-400 to-cyan-600"},10:{title:"Advanced Mathematics",topics:["Advanced Functions","Calculus Prep","Statistics","Discrete Math"],color:"from-emerald-400 to-emerald-600"}};function f(){let[e,t]=(0,o.useState)([]),r=e=>Math.round(e.completedTopics/e.totalTopics*100),i=e=>0===e?"Not Started":e<25?"Beginner":e<50?"Learning":e<75?"Progressing":e<100?"Advanced":"Mastered",f=e=>0===e?"text-gray-500":e<25?"text-red-500":e<50?"text-orange-500":e<75?"text-yellow-500":e<100?"text-blue-500":"text-green-500";return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)(a(),{href:"/",children:(0,s.jsxs)(p.$,{variant:"ghost",className:"mb-4",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Back to Home"]})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(l.A,{className:"h-8 w-8 text-purple-600 mr-3"}),(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-800",children:"Choose Your Grade"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Select your grade level to start your personalized math learning journey. Track your progress and earn achievements as you master each topic!"})]})]}),e.length>0&&(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)(x.Zp,{className:"p-6 text-center",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 text-yellow-500 mx-auto mb-2"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-1",children:"Total Score"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[e.reduce((e,t)=>e+t.totalScore,0)," points"]})]}),(0,s.jsxs)(x.Zp,{className:"p-6 text-center",children:[(0,s.jsx)(c.A,{className:"h-8 w-8 text-blue-500 mx-auto mb-2"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-1",children:"Time Spent"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-blue-600",children:[Math.round(e.reduce((e,t)=>e+t.timeSpent,0)/60)," hours"]})]}),(0,s.jsxs)(x.Zp,{className:"p-6 text-center",children:[(0,s.jsx)(m,{className:"h-8 w-8 text-green-500 mx-auto mb-2"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-1",children:"Topics Completed"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[e.reduce((e,t)=>e+t.completedTopics,0)," / ",40*e.length]})]})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:10},(e,t)=>t+1).map((t,o)=>{let n=e.find(e=>e.grade===t),l=n?r(n):0,d=g[t];return(0,s.jsx)(h.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*o},children:(0,s.jsx)(a(),{href:`/grade/${t}`,children:(0,s.jsxs)(x.Zp,{className:"p-6 hover:shadow-lg transition-all hover:scale-105 cursor-pointer group h-full",children:[(0,s.jsxs)("div",{className:`bg-gradient-to-r ${d.color} p-4 rounded-lg mb-4 text-white text-center`,children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold",children:["Grade ",t]}),(0,s.jsx)("p",{className:"text-sm opacity-90",children:d.title})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Progress"}),(0,s.jsx)("span",{className:`text-sm font-semibold ${f(l)}`,children:i(l)})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:`bg-gradient-to-r ${d.color} h-2 rounded-full transition-all duration-500`,style:{width:`${l}%`}})}),(0,s.jsxs)("p",{className:"text-xs text-gray-600 mt-1",children:[n?.completedTopics||0," / 40 topics completed"]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold mb-2 text-gray-700",children:"Key Topics:"}),(0,s.jsx)("div",{className:"space-y-1",children:d.topics.map((e,t)=>(0,s.jsxs)("div",{className:"text-xs text-gray-600 flex items-center",children:[(0,s.jsx)("div",{className:"w-1 h-1 bg-purple-400 rounded-full mr-2"}),e]},t))})]}),n&&n.totalScore>0&&(0,s.jsx)("div",{className:"border-t pt-3 mt-3",children:(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-600",children:[(0,s.jsxs)("span",{children:["Score: ",n.totalScore]}),(0,s.jsxs)("span",{children:["Time: ",Math.round(n.timeSpent),"m"]})]})}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)(p.$,{className:"w-full group-hover:bg-purple-600 transition-colors",size:"sm",children:[0===l?"Start Learning":"Continue Learning"," →"]})}),(0,s.jsx)("div",{className:"flex justify-center mt-3 space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsx)(u.A,{className:`h-3 w-3 ${t<Math.floor(l/20)?"text-yellow-400 fill-current":"text-gray-300"}`},t))})]})})},t)})}),(0,s.jsxs)("div",{className:"mt-12 text-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Want to try some games first?"}),(0,s.jsx)(a(),{href:"/demo",children:(0,s.jsx)(p.$,{variant:"outline",size:"lg",children:"Try Interactive Demo Games"})})]})]})})}},8470:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(5239),i=r(8088),a=r(8170),o=r.n(a),n=r(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["grades",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3159)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\grades\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\grades\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/grades/page",pathname:"/grades",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,70,567,135],()=>r(8470));module.exports=s})();