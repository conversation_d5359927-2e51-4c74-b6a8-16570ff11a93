// Data Initialization Script for MathQuest
// Populates MongoDB with comprehensive curriculum, quiz, and sample data

const { MongoClient } = require('mongodb');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/mathquest';

// Sample data
const sampleUsers = [
  {
    _id: "user1",
    name: "<PERSON>",
    email: "<EMAIL>",
    password: "$2b$10$hashedpassword1", // In real app, this would be properly hashed
    role: "student",
    grade: 1,
    avatar: "👧",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: "user2",
    name: "<PERSON>",
    email: "<EMAIL>",
    password: "$2b$10$hashedpassword2",
    role: "student",
    grade: 2,
    avatar: "👦",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: "user3",
    name: "<PERSON>",
    email: "<EMAIL>",
    password: "$2b$10$hashedpassword3",
    role: "student",
    grade: 3,
    avatar: "👧",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: "user4",
    name: "<PERSON>",
    email: "<EMAIL>",
    password: "$2b$10$hashedpassword4",
    role: "student",
    grade: 4,
    avatar: "👦",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: "user5",
    name: "Olivia Brown",
    email: "<EMAIL>",
    password: "$2b$10$hashedpassword5",
    role: "student",
    grade: 5,
    avatar: "👧",
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Sample achievements
const achievements = [
  {
    id: 'first_lesson',
    name: 'First Steps',
    description: 'Complete your very first lesson',
    icon: '🎯',
    category: 'completion',
    points: 10,
    requirements: { type: 'lessons_completed', value: 1 },
    rarity: 'common',
    createdAt: new Date()
  },
  {
    id: 'week_warrior',
    name: 'Week Warrior',
    description: 'Complete all lessons in a week',
    icon: '⚔️',
    category: 'completion',
    points: 50,
    requirements: { type: 'week_completed', value: 1 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'perfect_score',
    name: 'Perfect Score',
    description: 'Get 100% on any quiz',
    icon: '💯',
    category: 'score',
    points: 25,
    requirements: { type: 'perfect_score', value: 100 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'month_master',
    name: 'Month Master',
    description: 'Complete 4 weeks of lessons',
    icon: '👑',
    category: 'completion',
    points: 200,
    requirements: { type: 'weeks_completed', value: 4 },
    rarity: 'epic',
    createdAt: new Date()
  },
  {
    id: 'daily_learner',
    name: 'Daily Learner',
    description: 'Complete lessons for 3 days in a row',
    icon: '📅',
    category: 'streak',
    points: 30,
    requirements: { type: 'daily_streak', value: 3 },
    rarity: 'common',
    createdAt: new Date()
  },
  {
    id: 'week_streak',
    name: 'Week Streak',
    description: 'Complete lessons for 7 days in a row',
    icon: '🔥',
    category: 'streak',
    points: 100,
    requirements: { type: 'daily_streak', value: 7 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'high_achiever',
    name: 'High Achiever',
    description: 'Score 90% or higher on 5 quizzes',
    icon: '⭐',
    category: 'score',
    points: 75,
    requirements: { type: 'high_scores', value: 5 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'counting_master',
    name: 'Counting Master',
    description: 'Master all counting lessons in Grade 1',
    icon: '🔢',
    category: 'mastery',
    points: 150,
    requirements: { type: 'topic_mastery', value: 100, grade: 1, subject: 'counting' },
    rarity: 'epic',
    createdAt: new Date()
  },
  {
    id: 'addition_ace',
    name: 'Addition Ace',
    description: 'Master all addition lessons in Grade 1',
    icon: '➕',
    category: 'mastery',
    points: 150,
    requirements: { type: 'topic_mastery', value: 100, grade: 1, subject: 'addition' },
    rarity: 'epic',
    createdAt: new Date()
  },
  {
    id: 'multiplication_master',
    name: 'Multiplication Master',
    description: 'Master multiplication tables 1-10',
    icon: '✖️',
    category: 'mastery',
    points: 300,
    requirements: { type: 'topic_mastery', value: 100, grade: 3, subject: 'multiplication' },
    rarity: 'legendary',
    createdAt: new Date()
  }
];

// Generate sample progress data
function generateSampleProgress() {
  const progressData = [];
  const userProgressMap = {
    "user1": { completedWeeks: 8, grade: 1 },
    "user2": { completedWeeks: 12, grade: 2 },
    "user3": { completedWeeks: 15, grade: 3 },
    "user4": { completedWeeks: 20, grade: 4 },
    "user5": { completedWeeks: 25, grade: 5 }
  };

  Object.entries(userProgressMap).forEach(([userId, userData]) => {
    for (let week = 1; week <= userData.completedWeeks; week++) {
      const topicId = `grade${userData.grade}_week${week}`;
      const baseScore = 70 + Math.random() * 30;
      const timeSpent = 15 + Math.random() * 25;
      const attempts = Math.random() < 0.8 ? 1 : Math.floor(Math.random() * 3) + 1;
      
      progressData.push({
        userId: userId,
        grade: userData.grade,
        week: week,
        topicId: topicId,
        completed: true,
        score: Math.round(baseScore),
        timeSpent: Math.round(timeSpent),
        attempts: attempts,
        lastAttemptDate: new Date(Date.now() - (userData.completedWeeks - week) * 7 * 24 * 60 * 60 * 1000),
        achievements: [],
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
  });

  return progressData;
}

// Main initialization function
async function initializeData() {
  let client;
  
  try {
    console.log('Connecting to MongoDB...');
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db();
    console.log('Connected to database successfully!');

    // Clear existing data
    console.log('Clearing existing data...');
    await db.collection('users').deleteMany({});
    await db.collection('achievements').deleteMany({});
    await db.collection('progress').deleteMany({});

    // Insert sample users
    console.log('Inserting sample users...');
    await db.collection('users').insertMany(sampleUsers);
    console.log(`Inserted ${sampleUsers.length} sample users`);

    // Insert achievements
    console.log('Inserting achievements...');
    await db.collection('achievements').insertMany(achievements);
    console.log(`Inserted ${achievements.length} achievements`);

    // Insert sample progress
    console.log('Generating and inserting sample progress...');
    const progressData = generateSampleProgress();
    await db.collection('progress').insertMany(progressData);
    console.log(`Inserted ${progressData.length} progress records`);

    // Create indexes for better performance
    console.log('Creating database indexes...');
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('achievements').createIndex({ id: 1 }, { unique: true });
    await db.collection('achievements').createIndex({ category: 1, rarity: 1 });
    await db.collection('progress').createIndex({ userId: 1, grade: 1, week: 1 });
    await db.collection('progress').createIndex({ userId: 1, completed: 1 });
    console.log('Database indexes created successfully!');

    console.log('\n🎉 Data initialization completed successfully!');
    console.log('\nSample data summary:');
    console.log(`- ${sampleUsers.length} sample users created`);
    console.log(`- ${achievements.length} achievements defined`);
    console.log(`- ${progressData.length} progress records generated`);
    console.log('\nYou can now start the application and explore the sample data!');

  } catch (error) {
    console.error('Error initializing data:', error);
    process.exit(1);
  } finally {
    if (client) {
      await client.close();
      console.log('Database connection closed.');
    }
  }
}

// Run the initialization
if (require.main === module) {
  initializeData();
}

module.exports = { initializeData };
