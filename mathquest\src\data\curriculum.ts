// Comprehensive Mathematics Curriculum for Grades 1-10
// 40 weeks per academic year, structured week by week

export interface LearningOutcome {
  id: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface WeeklyTopic {
  week: number;
  title: string;
  description: string;
  topics: string[];
  learningOutcomes: LearningOutcome[];
  gameTypes: string[];
  estimatedHours: number;
}

export interface GradeCurriculum {
  grade: number;
  title: string;
  description: string;
  totalWeeks: number;
  weeks: WeeklyTopic[];
}

export const mathCurriculum: GradeCurriculum[] = [
  {
    grade: 1,
    title: "Grade 1 Mathematics - Foundation Building",
    description: "Introduction to numbers, basic counting, shapes, and simple addition/subtraction",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Numbers 1-10",
        description: "Introduction to counting and recognizing numbers 1-10",
        topics: ["Counting objects", "Number recognition", "Number writing", "One-to-one correspondence"],
        learningOutcomes: [
          { id: "g1w1_1", description: "Count objects up to 10", difficulty: "easy" },
          { id: "g1w1_2", description: "Recognize and write numbers 1-10", difficulty: "easy" },
          { id: "g1w1_3", description: "Match numbers to quantities", difficulty: "easy" }
        ],
        gameTypes: ["counting-game", "number-matching", "drag-drop"],
        estimatedHours: 4
      },
      {
        week: 2,
        title: "Numbers 11-20",
        description: "Extending counting skills to numbers 11-20",
        topics: ["Counting 11-20", "Teen numbers", "Number patterns", "Before and after"],
        learningOutcomes: [
          { id: "g1w2_1", description: "Count objects up to 20", difficulty: "easy" },
          { id: "g1w2_2", description: "Identify numbers that come before and after", difficulty: "medium" },
          { id: "g1w2_3", description: "Recognize patterns in teen numbers", difficulty: "medium" }
        ],
        gameTypes: ["number-sequence", "pattern-recognition", "counting-game"],
        estimatedHours: 4
      },
      {
        week: 3,
        title: "Basic Shapes",
        description: "Introduction to 2D shapes and their properties",
        topics: ["Circle", "Square", "Triangle", "Rectangle", "Shape recognition"],
        learningOutcomes: [
          { id: "g1w3_1", description: "Identify basic 2D shapes", difficulty: "easy" },
          { id: "g1w3_2", description: "Describe shape properties", difficulty: "medium" },
          { id: "g1w3_3", description: "Find shapes in the environment", difficulty: "easy" }
        ],
        gameTypes: ["shape-matching", "shape-hunt", "drawing-game"],
        estimatedHours: 4
      },
      {
        week: 4,
        title: "Comparing Numbers",
        description: "Learning to compare quantities and numbers",
        topics: ["More than", "Less than", "Equal to", "Comparing groups", "Number comparison"],
        learningOutcomes: [
          { id: "g1w4_1", description: "Compare two groups of objects", difficulty: "easy" },
          { id: "g1w4_2", description: "Use terms more, less, equal", difficulty: "easy" },
          { id: "g1w4_3", description: "Compare numbers 1-20", difficulty: "medium" }
        ],
        gameTypes: ["comparison-game", "balance-scale", "sorting-game"],
        estimatedHours: 4
      },
      {
        week: 5,
        title: "Addition Introduction",
        description: "Basic addition concepts with objects and pictures",
        topics: ["Adding objects", "Addition stories", "Plus sign", "Sum", "Addition facts to 5"],
        learningOutcomes: [
          { id: "g1w5_1", description: "Add two groups of objects", difficulty: "easy" },
          { id: "g1w5_2", description: "Write simple addition sentences", difficulty: "medium" },
          { id: "g1w5_3", description: "Solve addition problems to 5", difficulty: "easy" }
        ],
        gameTypes: ["addition-blocks", "story-problems", "number-bonds"],
        estimatedHours: 5
      },
      {
        week: 6,
        title: "Subtraction Introduction",
        description: "Basic subtraction concepts with objects and pictures",
        topics: ["Taking away objects", "Subtraction stories", "Minus sign", "Difference", "Subtraction facts to 5"],
        learningOutcomes: [
          { id: "g1w6_1", description: "Subtract objects from a group", difficulty: "easy" },
          { id: "g1w6_2", description: "Write simple subtraction sentences", difficulty: "medium" },
          { id: "g1w6_3", description: "Solve subtraction problems to 5", difficulty: "easy" }
        ],
        gameTypes: ["subtraction-blocks", "story-problems", "number-bonds"],
        estimatedHours: 5
      },
      {
        week: 7,
        title: "Addition and Subtraction to 10",
        description: "Extending addition and subtraction skills to 10",
        topics: ["Addition facts to 10", "Subtraction facts to 10", "Fact families", "Missing numbers"],
        learningOutcomes: [
          { id: "g1w7_1", description: "Add and subtract within 10 fluently", difficulty: "medium" },
          { id: "g1w7_2", description: "Understand fact families", difficulty: "medium" },
          { id: "g1w7_3", description: "Find missing numbers in equations", difficulty: "hard" }
        ],
        gameTypes: ["fact-family-house", "missing-number", "speed-math"],
        estimatedHours: 6
      },
      {
        week: 8,
        title: "Measurement - Length",
        description: "Introduction to measuring length using non-standard units",
        topics: ["Comparing lengths", "Measuring with objects", "Longer/shorter", "Units of measurement"],
        learningOutcomes: [
          { id: "g1w8_1", description: "Compare lengths of objects", difficulty: "easy" },
          { id: "g1w8_2", description: "Measure using non-standard units", difficulty: "medium" },
          { id: "g1w8_3", description: "Order objects by length", difficulty: "medium" }
        ],
        gameTypes: ["measurement-game", "comparison-tool", "ordering-activity"],
        estimatedHours: 4
      },
      {
        week: 9,
        title: "Time - Introduction",
        description: "Learning about time concepts and reading clocks",
        topics: ["Day and night", "Days of week", "Months", "Clock reading", "Hour and minute"],
        learningOutcomes: [
          { id: "g1w9_1", description: "Identify day and night activities", difficulty: "easy" },
          { id: "g1w9_2", description: "Name days of the week in order", difficulty: "easy" },
          { id: "g1w9_3", description: "Read time to the hour", difficulty: "medium" }
        ],
        gameTypes: ["time-matching", "clock-reading", "sequence-game"],
        estimatedHours: 4
      },
      {
        week: 10,
        title: "Money - Introduction",
        description: "Recognizing coins and their values",
        topics: ["Penny", "Nickel", "Dime", "Quarter", "Coin recognition", "Counting money"],
        learningOutcomes: [
          { id: "g1w10_1", description: "Identify different coins", difficulty: "easy" },
          { id: "g1w10_2", description: "Know coin values", difficulty: "medium" },
          { id: "g1w10_3", description: "Count simple coin combinations", difficulty: "hard" }
        ],
        gameTypes: ["coin-matching", "money-counting", "shopping-game"],
        estimatedHours: 5
      },
      // Continue with weeks 11-40 for Grade 1
      {
        week: 11,
        title: "Patterns and Sorting",
        description: "Identifying and creating patterns with objects and numbers",
        topics: ["Color patterns", "Shape patterns", "Number patterns", "Sorting by attributes"],
        learningOutcomes: [
          { id: "g1w11_1", description: "Identify and continue simple patterns", difficulty: "medium" },
          { id: "g1w11_2", description: "Create patterns with objects", difficulty: "medium" },
          { id: "g1w11_3", description: "Sort objects by multiple attributes", difficulty: "medium" }
        ],
        gameTypes: ["pattern-builder", "sorting-game", "pattern-recognition"],
        estimatedHours: 4
      },
      {
        week: 12,
        title: "Data and Graphs",
        description: "Introduction to collecting and organizing data",
        topics: ["Collecting data", "Tally marks", "Simple graphs", "Comparing data"],
        learningOutcomes: [
          { id: "g1w12_1", description: "Collect and organize simple data", difficulty: "medium" },
          { id: "g1w12_2", description: "Use tally marks to count", difficulty: "easy" },
          { id: "g1w12_3", description: "Read simple pictographs", difficulty: "medium" }
        ],
        gameTypes: ["data-collection", "graph-builder", "survey-game"],
        estimatedHours: 4
      },
      {
        week: 13,
        title: "Position and Direction",
        description: "Learning about spatial relationships and directions",
        topics: ["Above/below", "Left/right", "In front/behind", "Near/far"],
        learningOutcomes: [
          { id: "g1w13_1", description: "Use positional words correctly", difficulty: "easy" },
          { id: "g1w13_2", description: "Give and follow simple directions", difficulty: "medium" },
          { id: "g1w13_3", description: "Describe object positions", difficulty: "easy" }
        ],
        gameTypes: ["position-game", "direction-following", "spatial-puzzle"],
        estimatedHours: 4
      },
      {
        week: 14,
        title: "Addition to 20",
        description: "Extending addition skills to numbers up to 20",
        topics: ["Adding to 20", "Double facts", "Near doubles", "Mental strategies"],
        learningOutcomes: [
          { id: "g1w14_1", description: "Add single digits to make sums to 20", difficulty: "medium" },
          { id: "g1w14_2", description: "Use doubles and near doubles", difficulty: "hard" },
          { id: "g1w14_3", description: "Apply mental math strategies", difficulty: "hard" }
        ],
        gameTypes: ["addition-race", "doubles-game", "mental-math"],
        estimatedHours: 5
      },
      {
        week: 15,
        title: "Subtraction from 20",
        description: "Extending subtraction skills from numbers up to 20",
        topics: ["Subtracting from 20", "Counting back", "Fact families to 20", "Word problems"],
        learningOutcomes: [
          { id: "g1w15_1", description: "Subtract from numbers up to 20", difficulty: "medium" },
          { id: "g1w15_2", description: "Use counting back strategy", difficulty: "medium" },
          { id: "g1w15_3", description: "Solve simple word problems", difficulty: "hard" }
        ],
        gameTypes: ["subtraction-race", "counting-back", "word-problems"],
        estimatedHours: 5
      },
      // Continue with remaining weeks 16-40 for Grade 1
      {
        week: 16,
        title: "3D Shapes",
        description: "Introduction to three-dimensional shapes",
        topics: ["Cube", "Sphere", "Cylinder", "Cone", "Shape properties"],
        learningOutcomes: [
          { id: "g1w16_1", description: "Identify basic 3D shapes", difficulty: "medium" },
          { id: "g1w16_2", description: "Describe 3D shape properties", difficulty: "medium" },
          { id: "g1w16_3", description: "Find 3D shapes in environment", difficulty: "easy" }
        ],
        gameTypes: ["3d-shape-hunt", "shape-building", "property-matching"],
        estimatedHours: 4
      },
      {
        week: 17,
        title: "Fractions Introduction",
        description: "Basic understanding of halves and quarters",
        topics: ["Half", "Quarter", "Equal parts", "Fraction of shapes"],
        learningOutcomes: [
          { id: "g1w17_1", description: "Identify halves and quarters", difficulty: "medium" },
          { id: "g1w17_2", description: "Divide shapes into equal parts", difficulty: "hard" },
          { id: "g1w17_3", description: "Recognize fractions in daily life", difficulty: "medium" }
        ],
        gameTypes: ["fraction-pizza", "shape-divider", "fraction-matching"],
        estimatedHours: 5
      },
      {
        week: 18,
        title: "Skip Counting",
        description: "Counting by 2s, 5s, and 10s",
        topics: ["Count by 2s", "Count by 5s", "Count by 10s", "Number patterns"],
        learningOutcomes: [
          { id: "g1w18_1", description: "Skip count by 2s to 20", difficulty: "medium" },
          { id: "g1w18_2", description: "Skip count by 5s to 50", difficulty: "medium" },
          { id: "g1w18_3", description: "Skip count by 10s to 100", difficulty: "easy" }
        ],
        gameTypes: ["skip-counting-race", "number-line-hop", "pattern-fill"],
        estimatedHours: 4
      },
      {
        week: 19,
        title: "Measurement - Weight",
        description: "Comparing and measuring weight",
        topics: ["Heavy/light", "Balance scale", "Comparing weights", "Ordering by weight"],
        learningOutcomes: [
          { id: "g1w19_1", description: "Compare weights of objects", difficulty: "easy" },
          { id: "g1w19_2", description: "Use balance scale", difficulty: "medium" },
          { id: "g1w19_3", description: "Order objects by weight", difficulty: "medium" }
        ],
        gameTypes: ["balance-game", "weight-comparison", "ordering-activity"],
        estimatedHours: 4
      },
      {
        week: 20,
        title: "Measurement - Capacity",
        description: "Understanding volume and capacity",
        topics: ["Full/empty", "More/less liquid", "Cups and containers", "Comparing capacity"],
        learningOutcomes: [
          { id: "g1w20_1", description: "Compare capacity of containers", difficulty: "easy" },
          { id: "g1w20_2", description: "Use terms full, empty, half-full", difficulty: "easy" },
          { id: "g1w20_3", description: "Order containers by capacity", difficulty: "medium" }
        ],
        gameTypes: ["pouring-game", "capacity-comparison", "container-sorting"],
        estimatedHours: 4
      },
      // Weeks 21-40 would continue with more advanced topics
      // For brevity, I'll add a few more key weeks and indicate the pattern
      {
        week: 30,
        title: "Review and Assessment",
        description: "Comprehensive review of all Grade 1 topics",
        topics: ["Number review", "Operations review", "Shapes review", "Measurement review"],
        learningOutcomes: [
          { id: "g1w30_1", description: "Demonstrate mastery of Grade 1 numbers", difficulty: "medium" },
          { id: "g1w30_2", description: "Solve mixed operation problems", difficulty: "hard" },
          { id: "g1w30_3", description: "Apply measurement skills", difficulty: "medium" }
        ],
        gameTypes: ["mixed-review", "assessment-game", "skill-check"],
        estimatedHours: 6
      },
      {
        week: 40,
        title: "Grade 1 Mastery Challenge",
        description: "Final assessment and preparation for Grade 2",
        topics: ["Comprehensive review", "Problem solving", "Grade 2 preview", "Celebration"],
        learningOutcomes: [
          { id: "g1w40_1", description: "Demonstrate Grade 1 mastery", difficulty: "hard" },
          { id: "g1w40_2", description: "Solve multi-step problems", difficulty: "hard" },
          { id: "g1w40_3", description: "Show readiness for Grade 2", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 2,
    title: "Grade 2 Mathematics - Building Fluency",
    description: "Strengthening number sense, place value, and basic operations",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Numbers to 100",
        description: "Understanding place value and counting to 100",
        topics: ["Counting to 100", "Place value", "Tens and ones", "Number patterns"],
        learningOutcomes: [
          { id: "g2w1_1", description: "Count by 1s, 2s, 5s, and 10s to 100", difficulty: "easy" },
          { id: "g2w1_2", description: "Understand place value in 2-digit numbers", difficulty: "medium" },
          { id: "g2w1_3", description: "Identify patterns in number sequences", difficulty: "medium" }
        ],
        gameTypes: ["place-value-blocks", "number-patterns", "counting-by"],
        estimatedHours: 5
      },
      {
        week: 2,
        title: "Addition with Regrouping",
        description: "Two-digit addition with and without regrouping",
        topics: ["Adding 2-digit numbers", "Regrouping", "Mental math strategies", "Estimation"],
        learningOutcomes: [
          { id: "g2w2_1", description: "Add 2-digit numbers without regrouping", difficulty: "medium" },
          { id: "g2w2_2", description: "Add 2-digit numbers with regrouping", difficulty: "hard" },
          { id: "g2w2_3", description: "Estimate sums", difficulty: "medium" }
        ],
        gameTypes: ["base-ten-blocks", "mental-math", "estimation-game"],
        estimatedHours: 6
      },
      {
        week: 3,
        title: "Subtraction with Regrouping",
        description: "Two-digit subtraction with and without regrouping",
        topics: ["Subtracting 2-digit numbers", "Borrowing", "Mental subtraction", "Word problems"],
        learningOutcomes: [
          { id: "g2w3_1", description: "Subtract 2-digit numbers without regrouping", difficulty: "medium" },
          { id: "g2w3_2", description: "Subtract 2-digit numbers with regrouping", difficulty: "hard" },
          { id: "g2w3_3", description: "Solve subtraction word problems", difficulty: "hard" }
        ],
        gameTypes: ["base-ten-blocks", "borrowing-game", "word-problems"],
        estimatedHours: 6
      },
      {
        week: 4,
        title: "Geometry - 2D Shapes",
        description: "Advanced study of 2D shapes and their properties",
        topics: ["Polygons", "Sides and vertices", "Symmetry", "Shape patterns"],
        learningOutcomes: [
          { id: "g2w4_1", description: "Identify polygons by number of sides", difficulty: "medium" },
          { id: "g2w4_2", description: "Find lines of symmetry", difficulty: "hard" },
          { id: "g2w4_3", description: "Create and extend shape patterns", difficulty: "medium" }
        ],
        gameTypes: ["shape-sorting", "symmetry-game", "pattern-builder"],
        estimatedHours: 5
      },
      {
        week: 5,
        title: "Measurement - Standard Units",
        description: "Introduction to standard units of measurement",
        topics: ["Inches", "Feet", "Centimeters", "Meters", "Measuring tools"],
        learningOutcomes: [
          { id: "g2w5_1", description: "Measure objects using inches and feet", difficulty: "medium" },
          { id: "g2w5_2", description: "Measure objects using centimeters", difficulty: "medium" },
          { id: "g2w5_3", description: "Choose appropriate measuring tools", difficulty: "medium" }
        ],
        gameTypes: ["measurement-practice", "unit-conversion", "tool-selection"],
        estimatedHours: 5
      },
      // Continue with remaining weeks for Grade 2
      {
        week: 6,
        title: "Time - Hours and Minutes",
        description: "Reading clocks and understanding time",
        topics: ["Analog clocks", "Digital clocks", "Half hour", "Quarter hour", "Elapsed time"],
        learningOutcomes: [
          { id: "g2w6_1", description: "Read time to the half hour", difficulty: "medium" },
          { id: "g2w6_2", description: "Read time to the quarter hour", difficulty: "hard" },
          { id: "g2w6_3", description: "Calculate elapsed time", difficulty: "hard" }
        ],
        gameTypes: ["clock-reading", "time-matching", "elapsed-time"],
        estimatedHours: 5
      },
      {
        week: 7,
        title: "Money - Counting Coins",
        description: "Advanced money concepts and making change",
        topics: ["Coin combinations", "Dollar bills", "Making change", "Money word problems"],
        learningOutcomes: [
          { id: "g2w7_1", description: "Count mixed coin combinations", difficulty: "hard" },
          { id: "g2w7_2", description: "Make equivalent amounts", difficulty: "hard" },
          { id: "g2w7_3", description: "Solve money word problems", difficulty: "hard" }
        ],
        gameTypes: ["money-counting", "change-making", "shopping-game"],
        estimatedHours: 6
      },
      // Continue with remaining weeks for Grade 2 (weeks 8-40)
      {
        week: 40,
        title: "Grade 2 Mastery Challenge",
        description: "Final assessment and preparation for Grade 3",
        topics: ["Comprehensive review", "Problem solving", "Grade 3 preview", "Celebration"],
        learningOutcomes: [
          { id: "g2w40_1", description: "Demonstrate Grade 2 mastery", difficulty: "hard" },
          { id: "g2w40_2", description: "Solve multi-step problems", difficulty: "hard" },
          { id: "g2w40_3", description: "Show readiness for Grade 3", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 3,
    title: "Grade 3 Mathematics - Expanding Skills",
    description: "Introduction to multiplication, division, fractions, and advanced geometry",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Numbers to 1000",
        description: "Understanding place value in 3-digit numbers",
        topics: ["Hundreds place", "3-digit numbers", "Expanded form", "Comparing large numbers"],
        learningOutcomes: [
          { id: "g3w1_1", description: "Read and write 3-digit numbers", difficulty: "medium" },
          { id: "g3w1_2", description: "Understand place value to hundreds", difficulty: "medium" },
          { id: "g3w1_3", description: "Compare and order 3-digit numbers", difficulty: "hard" }
        ],
        gameTypes: ["place-value-blocks", "number-comparison", "ordering-game"],
        estimatedHours: 5
      },
      {
        week: 2,
        title: "Multiplication Introduction",
        description: "Understanding multiplication as repeated addition",
        topics: ["Repeated addition", "Multiplication symbol", "Arrays", "Times tables 2-5"],
        learningOutcomes: [
          { id: "g3w2_1", description: "Understand multiplication concept", difficulty: "medium" },
          { id: "g3w2_2", description: "Create and read arrays", difficulty: "medium" },
          { id: "g3w2_3", description: "Memorize times tables 2-5", difficulty: "hard" }
        ],
        gameTypes: ["array-builder", "times-table-race", "multiplication-stories"],
        estimatedHours: 6
      },
      {
        week: 3,
        title: "Division Introduction",
        description: "Understanding division as sharing equally",
        topics: ["Equal sharing", "Division symbol", "Fact families", "Division by 2-5"],
        learningOutcomes: [
          { id: "g3w3_1", description: "Understand division concept", difficulty: "medium" },
          { id: "g3w3_2", description: "Solve simple division problems", difficulty: "hard" },
          { id: "g3w3_3", description: "Connect multiplication and division", difficulty: "hard" }
        ],
        gameTypes: ["sharing-game", "fact-family-house", "division-stories"],
        estimatedHours: 6
      },
      {
        week: 40,
        title: "Grade 3 Mastery Challenge",
        description: "Final assessment and preparation for Grade 4",
        topics: ["Comprehensive review", "Problem solving", "Grade 4 preview", "Celebration"],
        learningOutcomes: [
          { id: "g3w40_1", description: "Demonstrate Grade 3 mastery", difficulty: "hard" },
          { id: "g3w40_2", description: "Solve complex word problems", difficulty: "hard" },
          { id: "g3w40_3", description: "Show readiness for Grade 4", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 4,
    title: "Grade 4 Mathematics - Strengthening Concepts",
    description: "Multi-digit operations, decimals, area, perimeter, and data analysis",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Large Numbers",
        description: "Working with numbers up to 10,000",
        topics: ["4-digit numbers", "Place value", "Rounding", "Number patterns"],
        learningOutcomes: [
          { id: "g4w1_1", description: "Read and write 4-digit numbers", difficulty: "medium" },
          { id: "g4w1_2", description: "Round to nearest 10 and 100", difficulty: "hard" },
          { id: "g4w1_3", description: "Identify number patterns", difficulty: "medium" }
        ],
        gameTypes: ["place-value-game", "rounding-practice", "pattern-finder"],
        estimatedHours: 5
      },
      {
        week: 2,
        title: "Multi-digit Addition",
        description: "Adding large numbers with regrouping",
        topics: ["4-digit addition", "Multiple regrouping", "Estimation", "Word problems"],
        learningOutcomes: [
          { id: "g4w2_1", description: "Add 4-digit numbers", difficulty: "hard" },
          { id: "g4w2_2", description: "Estimate sums", difficulty: "medium" },
          { id: "g4w2_3", description: "Solve addition word problems", difficulty: "hard" }
        ],
        gameTypes: ["addition-challenge", "estimation-game", "word-problems"],
        estimatedHours: 6
      },
      {
        week: 40,
        title: "Grade 4 Mastery Challenge",
        description: "Final assessment and preparation for Grade 5",
        topics: ["Comprehensive review", "Problem solving", "Grade 5 preview", "Celebration"],
        learningOutcomes: [
          { id: "g4w40_1", description: "Demonstrate Grade 4 mastery", difficulty: "hard" },
          { id: "g4w40_2", description: "Solve multi-step problems", difficulty: "hard" },
          { id: "g4w40_3", description: "Show readiness for Grade 5", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 5,
    title: "Grade 5 Mathematics - Advanced Operations",
    description: "Fractions, decimals, volume, coordinate plane, and algebraic thinking",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Decimal Place Value",
        description: "Understanding decimals to hundredths",
        topics: ["Decimal notation", "Tenths and hundredths", "Comparing decimals", "Decimal number line"],
        learningOutcomes: [
          { id: "g5w1_1", description: "Read and write decimals", difficulty: "medium" },
          { id: "g5w1_2", description: "Compare and order decimals", difficulty: "hard" },
          { id: "g5w1_3", description: "Place decimals on number line", difficulty: "hard" }
        ],
        gameTypes: ["decimal-builder", "decimal-comparison", "number-line-game"],
        estimatedHours: 6
      },
      {
        week: 40,
        title: "Grade 5 Mastery Challenge",
        description: "Final assessment and preparation for Grade 6",
        topics: ["Comprehensive review", "Problem solving", "Grade 6 preview", "Celebration"],
        learningOutcomes: [
          { id: "g5w40_1", description: "Demonstrate Grade 5 mastery", difficulty: "hard" },
          { id: "g5w40_2", description: "Solve complex problems", difficulty: "hard" },
          { id: "g5w40_3", description: "Show readiness for Grade 6", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 6,
    title: "Grade 6 Mathematics - Pre-Algebra Prep",
    description: "Ratios, percentages, integers, and algebraic expressions",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Ratios and Rates",
        description: "Understanding ratios and unit rates",
        topics: ["Ratio notation", "Equivalent ratios", "Unit rates", "Rate problems"],
        learningOutcomes: [
          { id: "g6w1_1", description: "Write and interpret ratios", difficulty: "medium" },
          { id: "g6w1_2", description: "Find equivalent ratios", difficulty: "hard" },
          { id: "g6w1_3", description: "Calculate unit rates", difficulty: "hard" }
        ],
        gameTypes: ["ratio-builder", "rate-calculator", "proportion-solver"],
        estimatedHours: 6
      },
      {
        week: 40,
        title: "Grade 6 Mastery Challenge",
        description: "Final assessment and preparation for Grade 7",
        topics: ["Comprehensive review", "Problem solving", "Grade 7 preview", "Celebration"],
        learningOutcomes: [
          { id: "g6w40_1", description: "Demonstrate Grade 6 mastery", difficulty: "hard" },
          { id: "g6w40_2", description: "Solve ratio and percentage problems", difficulty: "hard" },
          { id: "g6w40_3", description: "Show readiness for Grade 7", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 7,
    title: "Grade 7 Mathematics - Algebraic Thinking",
    description: "Equations, proportions, geometry, and probability",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Integers and Operations",
        description: "Working with positive and negative numbers",
        topics: ["Integer concepts", "Adding integers", "Subtracting integers", "Number line"],
        learningOutcomes: [
          { id: "g7w1_1", description: "Understand integer concepts", difficulty: "medium" },
          { id: "g7w1_2", description: "Add and subtract integers", difficulty: "hard" },
          { id: "g7w1_3", description: "Use number line with integers", difficulty: "medium" }
        ],
        gameTypes: ["integer-game", "number-line-battle", "operation-practice"],
        estimatedHours: 6
      },
      {
        week: 40,
        title: "Grade 7 Mastery Challenge",
        description: "Final assessment and preparation for Grade 8",
        topics: ["Comprehensive review", "Problem solving", "Grade 8 preview", "Celebration"],
        learningOutcomes: [
          { id: "g7w40_1", description: "Demonstrate Grade 7 mastery", difficulty: "hard" },
          { id: "g7w40_2", description: "Solve algebraic equations", difficulty: "hard" },
          { id: "g7w40_3", description: "Show readiness for Grade 8", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 8,
    title: "Grade 8 Mathematics - Advanced Algebra",
    description: "Linear functions, systems of equations, transformations, and statistics",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Linear Equations",
        description: "Solving and graphing linear equations",
        topics: ["Slope-intercept form", "Graphing lines", "Solving equations", "Real-world applications"],
        learningOutcomes: [
          { id: "g8w1_1", description: "Graph linear equations", difficulty: "hard" },
          { id: "g8w1_2", description: "Find slope and y-intercept", difficulty: "hard" },
          { id: "g8w1_3", description: "Solve linear equations", difficulty: "hard" }
        ],
        gameTypes: ["graphing-game", "slope-finder", "equation-solver"],
        estimatedHours: 7
      },
      {
        week: 40,
        title: "Grade 8 Mastery Challenge",
        description: "Final assessment and preparation for Grade 9",
        topics: ["Comprehensive review", "Problem solving", "Grade 9 preview", "Celebration"],
        learningOutcomes: [
          { id: "g8w40_1", description: "Demonstrate Grade 8 mastery", difficulty: "hard" },
          { id: "g8w40_2", description: "Solve systems of equations", difficulty: "hard" },
          { id: "g8w40_3", description: "Show readiness for Grade 9", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 9,
    title: "Grade 9 Mathematics - High School Prep",
    description: "Quadratics, exponentials, trigonometry basics, and polynomials",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Quadratic Functions",
        description: "Introduction to quadratic equations and parabolas",
        topics: ["Quadratic form", "Parabolas", "Vertex form", "Factoring"],
        learningOutcomes: [
          { id: "g9w1_1", description: "Identify quadratic functions", difficulty: "hard" },
          { id: "g9w1_2", description: "Graph parabolas", difficulty: "hard" },
          { id: "g9w1_3", description: "Find vertex and axis of symmetry", difficulty: "hard" }
        ],
        gameTypes: ["parabola-grapher", "vertex-finder", "factoring-game"],
        estimatedHours: 8
      },
      {
        week: 40,
        title: "Grade 9 Mastery Challenge",
        description: "Final assessment and preparation for Grade 10",
        topics: ["Comprehensive review", "Problem solving", "Grade 10 preview", "Celebration"],
        learningOutcomes: [
          { id: "g9w40_1", description: "Demonstrate Grade 9 mastery", difficulty: "hard" },
          { id: "g9w40_2", description: "Solve quadratic equations", difficulty: "hard" },
          { id: "g9w40_3", description: "Show readiness for Grade 10", difficulty: "medium" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "grade-preview"],
        estimatedHours: 8
      }
    ]
  },
  {
    grade: 10,
    title: "Grade 10 Mathematics - Advanced Mathematics",
    description: "Advanced functions, calculus preparation, statistics, and discrete mathematics",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Advanced Functions",
        description: "Polynomial, rational, and exponential functions",
        topics: ["Function composition", "Inverse functions", "Exponential growth", "Logarithms"],
        learningOutcomes: [
          { id: "g10w1_1", description: "Compose and decompose functions", difficulty: "hard" },
          { id: "g10w1_2", description: "Find inverse functions", difficulty: "hard" },
          { id: "g10w1_3", description: "Work with exponential functions", difficulty: "hard" }
        ],
        gameTypes: ["function-composer", "inverse-finder", "exponential-explorer"],
        estimatedHours: 8
      },
      {
        week: 40,
        title: "Grade 10 Mastery Challenge",
        description: "Final assessment and preparation for advanced mathematics",
        topics: ["Comprehensive review", "Problem solving", "Advanced math preview", "Celebration"],
        learningOutcomes: [
          { id: "g10w40_1", description: "Demonstrate Grade 10 mastery", difficulty: "hard" },
          { id: "g10w40_2", description: "Solve complex function problems", difficulty: "hard" },
          { id: "g10w40_3", description: "Show readiness for advanced math", difficulty: "hard" }
        ],
        gameTypes: ["mastery-challenge", "problem-solving", "advanced-preview"],
        estimatedHours: 10
      }
    ]
  }
];

// This is a sample structure - in production, this would include all 40 weeks for each grade
export const getGradeCurriculum = (grade: number): GradeCurriculum | undefined => {
  return mathCurriculum.find(curriculum => curriculum.grade === grade);
};

export const getWeeklyTopic = (grade: number, week: number): WeeklyTopic | undefined => {
  const gradeCurriculum = getGradeCurriculum(grade);
  return gradeCurriculum?.weeks.find(weeklyTopic => weeklyTopic.week === week);
};
