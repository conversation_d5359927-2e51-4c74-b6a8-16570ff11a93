# MathQuest Deployment Test Script
# This script tests if the Docker deployment is working correctly

Write-Host "🧪 MathQuest Deployment Test" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if <PERSON><PERSON> is running
Write-Host "1. Testing Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "   ✅ Docker is running: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Docker is not running or not installed" -ForegroundColor Red
    exit 1
}

# Test 2: Check if containers are running
Write-Host "2. Testing containers..." -ForegroundColor Yellow
$containers = docker ps --filter "name=mathquest" --format "table {{.Names}}\t{{.Status}}"
if ($containers -match "mathquest-app" -and $containers -match "mathquest-mongodb") {
    Write-Host "   ✅ MathQuest containers are running" -ForegroundColor Green
} else {
    Write-Host "   ❌ MathQuest containers are not running" -ForegroundColor Red
    Write-Host "   Starting containers..." -ForegroundColor Yellow
    docker-compose up -d
    Start-Sleep -Seconds 10
}

# Test 3: Check application response
Write-Host "3. Testing application response..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✅ Application is responding (HTTP $($response.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  Application responded with HTTP $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ Application is not responding" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Check database connectivity
Write-Host "4. Testing database connectivity..." -ForegroundColor Yellow
try {
    $dbTest = docker exec mathquest-mongodb mongosh -u admin -p password123 --authenticationDatabase admin --eval "db.runCommand('ping')" --quiet
    if ($dbTest -match "ok.*1") {
        Write-Host "   ✅ Database is accessible" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Database connection failed" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Database test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Check sample data
Write-Host "5. Testing sample data..." -ForegroundColor Yellow
try {
    $userCount = docker exec mathquest-mongodb mongosh -u admin -p password123 --authenticationDatabase admin mathquest --eval "db.users.countDocuments()" --quiet
    $achievementCount = docker exec mathquest-mongodb mongosh -u admin -p password123 --authenticationDatabase admin mathquest --eval "db.achievements.countDocuments()" --quiet
    
    if ($userCount -match "\d+" -and $achievementCount -match "\d+") {
        Write-Host "   ✅ Sample data loaded: $($userCount.Trim()) users, $($achievementCount.Trim()) achievements" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  Sample data may not be loaded properly" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ Could not verify sample data: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Check specific routes
Write-Host "6. Testing application routes..." -ForegroundColor Yellow
$routes = @("/", "/grades", "/demo")
foreach ($route in $routes) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000$route" -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✅ Route $route is working" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  Route $route returned HTTP $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   ❌ Route $route failed" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎯 Test Summary" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host ""
Write-Host "If all tests passed, your MathQuest application is ready!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Access your application at: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎮 Sample login credentials:" -ForegroundColor Cyan
Write-Host "   Email: <EMAIL>" -ForegroundColor White
Write-Host "   Email: <EMAIL>" -ForegroundColor White
Write-Host "   Password: password123" -ForegroundColor White
Write-Host ""
Write-Host "📚 Available features:" -ForegroundColor Cyan
Write-Host "   • Grade selection (1-10)" -ForegroundColor White
Write-Host "   • Interactive curriculum" -ForegroundColor White
Write-Host "   • Achievement system" -ForegroundColor White
Write-Host "   • Progress tracking" -ForegroundColor White
Write-Host "   • Demo mode" -ForegroundColor White
Write-Host ""

# Open browser automatically
$openBrowser = Read-Host "Would you like to open the application in your browser? (y/n)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Process "http://localhost:3000"
    Write-Host "🚀 Opening MathQuest in your default browser..." -ForegroundColor Green
}
