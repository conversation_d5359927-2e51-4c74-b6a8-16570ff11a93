(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var o=t(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1880:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},2318:(e,r,t)=>{Promise.resolve().then(t.bind(t,7552))},2582:(e,r,t)=>{Promise.resolve().then(t.bind(t,3622))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3622:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>o});let o=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\components\\Providers.tsx","Providers")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>l,viewport:()=>i});var o=t(7413),s=t(5041),a=t.n(s);t(1135);var n=t(3622);let l={title:"MathQuest - Gamified Mathematics Learning",description:"Interactive mathematics learning platform for grades 1-10 with games, achievements, and personalized progress tracking",keywords:"mathematics, learning, education, games, kids, math, interactive",authors:[{name:"MathQuest Team"}]},i={width:"device-width",initialScale:1};function d({children:e}){return(0,o.jsx)("html",{lang:"en",className:"h-full",children:(0,o.jsx)("body",{className:`${a().className} h-full bg-gradient-to-br from-blue-50 to-purple-50 antialiased`,children:(0,o.jsx)(n.Providers,{children:e})})})}},4536:(e,r,t)=>{let{createProxy:o}=t(9844);e.exports=o("C:\\Users\\<USER>\\augument\\onstud\\mathquest\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4761:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,5543,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},6446:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},7552:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>a});var o=t(687),s=t(2136);function a({children:e}){return(0,o.jsx)(s.SessionProvider,{children:e})}},7785:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9394:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var o=t(5239),s=t(8088),a=t(8170),n=t.n(a),l=t(893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9951)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9551:e=>{"use strict";e.exports=require("url")},9951:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>e_});var o=t(7413),s=t(4536),a=t.n(s),n=t(1120);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),d=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},c=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),m=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var p={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:s="",children:a,iconNode:l,...i},d)=>(0,n.createElement)("svg",{ref:d,...p,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:c("lucide",s),...!a&&!m(i)&&{"aria-hidden":"true"},...i},[...l.map(([e,r])=>(0,n.createElement)(e,r)),...Array.isArray(a)?a:[a]])),h=(e,r)=>{let t=(0,n.forwardRef)(({className:t,...o},s)=>(0,n.createElement)(u,{ref:s,iconNode:r,className:c(`lucide-${l(d(e))}`,`lucide-${e}`,t),...o}));return t.displayName=d(e),t},f=h("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),g=h("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),b=h("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),x=h("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),v=h("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),w=h("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),y=e=>{let r=z(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),k(t,r)||N(e)},getConflictingClassGroupIds:(e,r)=>{let s=t[e]||[];return r&&o[e]?[...s,...o[e]]:s}}},k=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),s=o?k(e.slice(1),o):void 0;if(s)return s;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},j=/^\[(.+)\]$/,N=e=>{if(j.test(e)){let r=j.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},z=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)P(t[e],o,e,r);return o},P=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:M(r,e)).classGroupId=t;return}if("function"==typeof e)return C(e)?void P(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,s])=>{P(s,M(r,e),t,o)})})},M=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},C=e=>e.isThemeGetter,q=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,s=(s,a)=>{t.set(s,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(s(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):s(e,r)}}},G=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,s=0,a=0;for(let n=0;n<e.length;n++){let l=e[n];if(0===o&&0===s){if(":"===l){t.push(e.slice(a,n)),a=n+1;continue}if("/"===l){r=n;continue}}"["===l?o++:"]"===l?o--:"("===l?s++:")"===l&&s--}let n=0===t.length?e:e.substring(a),l=_(n);return{modifiers:t,hasImportantModifier:l!==n,baseClassName:l,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},_=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,A=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},E=e=>({cache:q(e.cacheSize),parseClassName:G(e),sortModifiers:A(e),...y(e)}),$=/\s+/,S=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:s,sortModifiers:a}=r,n=[],l=e.trim().split($),i="";for(let e=l.length-1;e>=0;e-=1){let r=l[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=t(r);if(d){i=r+(i.length>0?" "+i:i);continue}let h=!!u,f=o(h?p.substring(0,u):p);if(!f){if(!h||!(f=o(p))){i=r+(i.length>0?" "+i:i);continue}h=!1}let g=a(c).join(":"),b=m?g+"!":g,x=b+f;if(n.includes(x))continue;n.push(x);let v=s(f,h);for(let e=0;e<v.length;++e){let r=v[e];n.push(b+r)}i=r+(i.length>0?" "+i:i)}return i};function L(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=R(e))&&(o&&(o+=" "),o+=r);return o}let R=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=R(e[o]))&&(t&&(t+=" "),t+=r);return t},I=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},T=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,W=/^\((?:(\w[\w-]*):)?(.+)\)$/i,U=/^\d+\/\d+$/,H=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Q=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,O=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,D=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,F=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=e=>U.test(e),Z=e=>!!e&&!Number.isNaN(Number(e)),B=e=>!!e&&Number.isInteger(Number(e)),K=e=>e.endsWith("%")&&Z(e.slice(0,-1)),X=e=>H.test(e),J=()=>!0,Y=e=>Q.test(e)&&!O.test(e),ee=()=>!1,er=e=>D.test(e),et=e=>F.test(e),eo=e=>!ea(e)&&!em(e),es=e=>ex(e,ek,ee),ea=e=>T.test(e),en=e=>ex(e,ej,Y),el=e=>ex(e,eN,Z),ei=e=>ex(e,ew,ee),ed=e=>ex(e,ey,et),ec=e=>ex(e,eP,er),em=e=>W.test(e),ep=e=>ev(e,ej),eu=e=>ev(e,ez),eh=e=>ev(e,ew),ef=e=>ev(e,ek),eg=e=>ev(e,ey),eb=e=>ev(e,eP,!0),ex=(e,r,t)=>{let o=T.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},ev=(e,r,t=!1)=>{let o=W.exec(e);return!!o&&(o[1]?r(o[1]):t)},ew=e=>"position"===e||"percentage"===e,ey=e=>"image"===e||"url"===e,ek=e=>"length"===e||"size"===e||"bg-size"===e,ej=e=>"length"===e,eN=e=>"number"===e,ez=e=>"family-name"===e,eP=e=>"shadow"===e;Symbol.toStringTag;let eM=function(e,...r){let t,o,s,a=function(l){return o=(t=E(r.reduce((e,r)=>r(e),e()))).cache.get,s=t.cache.set,a=n,n(l)};function n(e){let r=o(e);if(r)return r;let a=S(e,t);return s(e,a),a}return function(){return a(L.apply(null,arguments))}}(()=>{let e=I("color"),r=I("font"),t=I("text"),o=I("font-weight"),s=I("tracking"),a=I("leading"),n=I("breakpoint"),l=I("container"),i=I("spacing"),d=I("radius"),c=I("shadow"),m=I("inset-shadow"),p=I("text-shadow"),u=I("drop-shadow"),h=I("blur"),f=I("perspective"),g=I("aspect"),b=I("ease"),x=I("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],y=()=>[...w(),em,ea],k=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],N=()=>[em,ea,i],z=()=>[V,"full","auto",...N()],P=()=>[B,"none","subgrid",em,ea],M=()=>["auto",{span:["full",B,em,ea]},B,em,ea],C=()=>[B,"auto",em,ea],q=()=>["auto","min","max","fr",em,ea],G=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],A=()=>["auto",...N()],E=()=>[V,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],$=()=>[e,em,ea],S=()=>[...w(),eh,ei,{position:[em,ea]}],L=()=>["no-repeat",{repeat:["","x","y","space","round"]}],R=()=>["auto","cover","contain",ef,es,{size:[em,ea]}],T=()=>[K,ep,en],W=()=>["","none","full",d,em,ea],U=()=>["",Z,ep,en],H=()=>["solid","dashed","dotted","double"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],O=()=>[Z,K,eh,ei],D=()=>["","none",h,em,ea],F=()=>["none",Z,em,ea],Y=()=>["none",Z,em,ea],ee=()=>[Z,em,ea],er=()=>[V,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[X],breakpoint:[X],color:[J],container:[X],"drop-shadow":[X],ease:["in","out","in-out"],font:[eo],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[X],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[X],shadow:[X],spacing:["px",Z],text:[X],"text-shadow":[X],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",V,ea,em,g]}],container:["container"],columns:[{columns:[Z,ea,em,l]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:y()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[B,"auto",em,ea]}],basis:[{basis:[V,"full","auto",l,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Z,V,"auto","initial","none",ea]}],grow:[{grow:["",Z,em,ea]}],shrink:[{shrink:["",Z,em,ea]}],order:[{order:[B,"first","last","none",em,ea]}],"grid-cols":[{"grid-cols":P()}],"col-start-end":[{col:M()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":P()}],"row-start-end":[{row:M()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":q()}],"auto-rows":[{"auto-rows":q()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...G(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...G()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":G()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:A()}],mx:[{mx:A()}],my:[{my:A()}],ms:[{ms:A()}],me:[{me:A()}],mt:[{mt:A()}],mr:[{mr:A()}],mb:[{mb:A()}],ml:[{ml:A()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:E()}],w:[{w:[l,"screen",...E()]}],"min-w":[{"min-w":[l,"screen","none",...E()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[n]},...E()]}],h:[{h:["screen","lh",...E()]}],"min-h":[{"min-h":["screen","lh","none",...E()]}],"max-h":[{"max-h":["screen","lh",...E()]}],"font-size":[{text:["base",t,ep,en]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,em,el]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",K,ea]}],"font-family":[{font:[eu,ea,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,em,ea]}],"line-clamp":[{"line-clamp":[Z,"none",em,el]}],leading:[{leading:[a,...N()]}],"list-image":[{"list-image":["none",em,ea]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",em,ea]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:$()}],"text-color":[{text:$()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:[Z,"from-font","auto",em,en]}],"text-decoration-color":[{decoration:$()}],"underline-offset":[{"underline-offset":[Z,"auto",em,ea]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",em,ea]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",em,ea]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:S()}],"bg-repeat":[{bg:L()}],"bg-size":[{bg:R()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},B,em,ea],radial:["",em,ea],conic:[B,em,ea]},eg,ed]}],"bg-color":[{bg:$()}],"gradient-from-pos":[{from:T()}],"gradient-via-pos":[{via:T()}],"gradient-to-pos":[{to:T()}],"gradient-from":[{from:$()}],"gradient-via":[{via:$()}],"gradient-to":[{to:$()}],rounded:[{rounded:W()}],"rounded-s":[{"rounded-s":W()}],"rounded-e":[{"rounded-e":W()}],"rounded-t":[{"rounded-t":W()}],"rounded-r":[{"rounded-r":W()}],"rounded-b":[{"rounded-b":W()}],"rounded-l":[{"rounded-l":W()}],"rounded-ss":[{"rounded-ss":W()}],"rounded-se":[{"rounded-se":W()}],"rounded-ee":[{"rounded-ee":W()}],"rounded-es":[{"rounded-es":W()}],"rounded-tl":[{"rounded-tl":W()}],"rounded-tr":[{"rounded-tr":W()}],"rounded-br":[{"rounded-br":W()}],"rounded-bl":[{"rounded-bl":W()}],"border-w":[{border:U()}],"border-w-x":[{"border-x":U()}],"border-w-y":[{"border-y":U()}],"border-w-s":[{"border-s":U()}],"border-w-e":[{"border-e":U()}],"border-w-t":[{"border-t":U()}],"border-w-r":[{"border-r":U()}],"border-w-b":[{"border-b":U()}],"border-w-l":[{"border-l":U()}],"divide-x":[{"divide-x":U()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":U()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...H(),"hidden","none"]}],"divide-style":[{divide:[...H(),"hidden","none"]}],"border-color":[{border:$()}],"border-color-x":[{"border-x":$()}],"border-color-y":[{"border-y":$()}],"border-color-s":[{"border-s":$()}],"border-color-e":[{"border-e":$()}],"border-color-t":[{"border-t":$()}],"border-color-r":[{"border-r":$()}],"border-color-b":[{"border-b":$()}],"border-color-l":[{"border-l":$()}],"divide-color":[{divide:$()}],"outline-style":[{outline:[...H(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Z,em,ea]}],"outline-w":[{outline:["",Z,ep,en]}],"outline-color":[{outline:$()}],shadow:[{shadow:["","none",c,eb,ec]}],"shadow-color":[{shadow:$()}],"inset-shadow":[{"inset-shadow":["none",m,eb,ec]}],"inset-shadow-color":[{"inset-shadow":$()}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:$()}],"ring-offset-w":[{"ring-offset":[Z,en]}],"ring-offset-color":[{"ring-offset":$()}],"inset-ring-w":[{"inset-ring":U()}],"inset-ring-color":[{"inset-ring":$()}],"text-shadow":[{"text-shadow":["none",p,eb,ec]}],"text-shadow-color":[{"text-shadow":$()}],opacity:[{opacity:[Z,em,ea]}],"mix-blend":[{"mix-blend":[...Q(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Q()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Z]}],"mask-image-linear-from-pos":[{"mask-linear-from":O()}],"mask-image-linear-to-pos":[{"mask-linear-to":O()}],"mask-image-linear-from-color":[{"mask-linear-from":$()}],"mask-image-linear-to-color":[{"mask-linear-to":$()}],"mask-image-t-from-pos":[{"mask-t-from":O()}],"mask-image-t-to-pos":[{"mask-t-to":O()}],"mask-image-t-from-color":[{"mask-t-from":$()}],"mask-image-t-to-color":[{"mask-t-to":$()}],"mask-image-r-from-pos":[{"mask-r-from":O()}],"mask-image-r-to-pos":[{"mask-r-to":O()}],"mask-image-r-from-color":[{"mask-r-from":$()}],"mask-image-r-to-color":[{"mask-r-to":$()}],"mask-image-b-from-pos":[{"mask-b-from":O()}],"mask-image-b-to-pos":[{"mask-b-to":O()}],"mask-image-b-from-color":[{"mask-b-from":$()}],"mask-image-b-to-color":[{"mask-b-to":$()}],"mask-image-l-from-pos":[{"mask-l-from":O()}],"mask-image-l-to-pos":[{"mask-l-to":O()}],"mask-image-l-from-color":[{"mask-l-from":$()}],"mask-image-l-to-color":[{"mask-l-to":$()}],"mask-image-x-from-pos":[{"mask-x-from":O()}],"mask-image-x-to-pos":[{"mask-x-to":O()}],"mask-image-x-from-color":[{"mask-x-from":$()}],"mask-image-x-to-color":[{"mask-x-to":$()}],"mask-image-y-from-pos":[{"mask-y-from":O()}],"mask-image-y-to-pos":[{"mask-y-to":O()}],"mask-image-y-from-color":[{"mask-y-from":$()}],"mask-image-y-to-color":[{"mask-y-to":$()}],"mask-image-radial":[{"mask-radial":[em,ea]}],"mask-image-radial-from-pos":[{"mask-radial-from":O()}],"mask-image-radial-to-pos":[{"mask-radial-to":O()}],"mask-image-radial-from-color":[{"mask-radial-from":$()}],"mask-image-radial-to-color":[{"mask-radial-to":$()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[Z]}],"mask-image-conic-from-pos":[{"mask-conic-from":O()}],"mask-image-conic-to-pos":[{"mask-conic-to":O()}],"mask-image-conic-from-color":[{"mask-conic-from":$()}],"mask-image-conic-to-color":[{"mask-conic-to":$()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:S()}],"mask-repeat":[{mask:L()}],"mask-size":[{mask:R()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",em,ea]}],filter:[{filter:["","none",em,ea]}],blur:[{blur:D()}],brightness:[{brightness:[Z,em,ea]}],contrast:[{contrast:[Z,em,ea]}],"drop-shadow":[{"drop-shadow":["","none",u,eb,ec]}],"drop-shadow-color":[{"drop-shadow":$()}],grayscale:[{grayscale:["",Z,em,ea]}],"hue-rotate":[{"hue-rotate":[Z,em,ea]}],invert:[{invert:["",Z,em,ea]}],saturate:[{saturate:[Z,em,ea]}],sepia:[{sepia:["",Z,em,ea]}],"backdrop-filter":[{"backdrop-filter":["","none",em,ea]}],"backdrop-blur":[{"backdrop-blur":D()}],"backdrop-brightness":[{"backdrop-brightness":[Z,em,ea]}],"backdrop-contrast":[{"backdrop-contrast":[Z,em,ea]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Z,em,ea]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Z,em,ea]}],"backdrop-invert":[{"backdrop-invert":["",Z,em,ea]}],"backdrop-opacity":[{"backdrop-opacity":[Z,em,ea]}],"backdrop-saturate":[{"backdrop-saturate":[Z,em,ea]}],"backdrop-sepia":[{"backdrop-sepia":["",Z,em,ea]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",em,ea]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Z,"initial",em,ea]}],ease:[{ease:["linear","initial",b,em,ea]}],delay:[{delay:[Z,em,ea]}],animate:[{animate:["none",x,em,ea]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,em,ea]}],"perspective-origin":[{"perspective-origin":y()}],rotate:[{rotate:F()}],"rotate-x":[{"rotate-x":F()}],"rotate-y":[{"rotate-y":F()}],"rotate-z":[{"rotate-z":F()}],scale:[{scale:Y()}],"scale-x":[{"scale-x":Y()}],"scale-y":[{"scale-y":Y()}],"scale-z":[{"scale-z":Y()}],"scale-3d":["scale-3d"],skew:[{skew:ee()}],"skew-x":[{"skew-x":ee()}],"skew-y":[{"skew-y":ee()}],transform:[{transform:[em,ea,"","none","gpu","cpu"]}],"transform-origin":[{origin:y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:er()}],"translate-x":[{"translate-x":er()}],"translate-y":[{"translate-y":er()}],"translate-z":[{"translate-z":er()}],"translate-none":["translate-none"],accent:[{accent:$()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:$()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",em,ea]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",em,ea]}],fill:[{fill:["none",...$()]}],"stroke-w":[{stroke:[Z,ep,en,el]}],stroke:[{stroke:["none",...$()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function eC(...e){return eM(function(){for(var e,r,t=0,o="",s=arguments.length;t<s;t++)(e=arguments[t])&&(r=function e(r){var t,o,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(s&&(s+=" "),s+=o)}else for(o in r)r[o]&&(s&&(s+=" "),s+=o);return s}(e))&&(o&&(o+=" "),o+=r);return o}(e))}let eq=(0,n.forwardRef)(({className:e,variant:r="default",size:t="default",...s},a)=>(0,o.jsx)("button",{className:eC("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===r,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===r,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===r,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===r,"hover:bg-accent hover:text-accent-foreground":"ghost"===r,"text-primary underline-offset-4 hover:underline":"link"===r},{"h-10 px-4 py-2":"default"===t,"h-9 rounded-md px-3":"sm"===t,"h-11 rounded-md px-8":"lg"===t,"h-10 w-10":"icon"===t},e),ref:a,...s}));eq.displayName="Button";let eG=(0,n.forwardRef)(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eC("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));function e_(){return(0,o.jsxs)("div",{className:"min-h-screen",children:[(0,o.jsx)("nav",{className:"bg-white/80 backdrop-blur-md border-b border-purple-100 sticky top-0 z-50",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 p-2 rounded-lg",children:(0,o.jsx)(f,{className:"h-6 w-6 text-white"})}),(0,o.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",children:"MathQuest"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)(a(),{href:"/demo",children:(0,o.jsx)(eq,{variant:"ghost",children:"Try Demo"})}),(0,o.jsx)(a(),{href:"/grades",children:(0,o.jsx)(eq,{children:"Start Learning"})})]})]})})}),(0,o.jsx)("section",{className:"relative py-20 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,o.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold mb-6",children:[(0,o.jsx)("span",{className:"bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 bg-clip-text text-transparent",children:"Math Learning"}),(0,o.jsx)("br",{}),(0,o.jsx)("span",{className:"text-gray-800",children:"Made Fun!"})]}),(0,o.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Transform your child's math journey with our gamified learning platform. Interactive lessons, exciting challenges, and personalized progress tracking for grades 1-10."}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,o.jsx)(a(),{href:"/grades",children:(0,o.jsxs)(eq,{size:"lg",className:"text-lg px-8 py-4",children:["Start Learning Today",(0,o.jsx)(g,{className:"ml-2 h-5 w-5"})]})}),(0,o.jsx)(a(),{href:"/demo",children:(0,o.jsx)(eq,{variant:"outline",size:"lg",className:"text-lg px-8 py-4",children:"Try Demo"})})]})]})}),(0,o.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8 bg-white/50",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,o.jsx)("h2",{className:"text-4xl font-bold text-center mb-16 text-gray-800",children:"Why Choose MathQuest?"}),(0,o.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,o.jsxs)(eG,{className:"p-6 text-center hover:shadow-lg transition-shadow",children:[(0,o.jsx)("div",{className:"bg-purple-100 p-3 rounded-full w-fit mx-auto mb-4",children:(0,o.jsx)(b,{className:"h-8 w-8 text-purple-600"})}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Gamified Learning"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Earn points, unlock achievements, and compete with friends while mastering math concepts."})]}),(0,o.jsxs)(eG,{className:"p-6 text-center hover:shadow-lg transition-shadow",children:[(0,o.jsx)("div",{className:"bg-blue-100 p-3 rounded-full w-fit mx-auto mb-4",children:(0,o.jsx)(f,{className:"h-8 w-8 text-blue-600"})}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Curriculum Aligned"}),(0,o.jsx)("p",{className:"text-gray-600",children:"40 weeks of structured content for each grade, following international math standards."})]}),(0,o.jsxs)(eG,{className:"p-6 text-center hover:shadow-lg transition-shadow",children:[(0,o.jsx)("div",{className:"bg-teal-100 p-3 rounded-full w-fit mx-auto mb-4",children:(0,o.jsx)(x,{className:"h-8 w-8 text-teal-600"})}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Progress Tracking"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Detailed analytics and reports to monitor learning progress and identify areas for improvement."})]}),(0,o.jsxs)(eG,{className:"p-6 text-center hover:shadow-lg transition-shadow",children:[(0,o.jsx)("div",{className:"bg-orange-100 p-3 rounded-full w-fit mx-auto mb-4",children:(0,o.jsx)(v,{className:"h-8 w-8 text-orange-600"})}),(0,o.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Family Friendly"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Safe environment with parental controls and teacher dashboards for comprehensive oversight."})]})]})]})}),(0,o.jsx)("section",{className:"py-20 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,o.jsx)("h2",{className:"text-4xl font-bold mb-16 text-gray-800",children:"Comprehensive Coverage for All Grades"}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[1,2,3,4,5,6,7,8,9,10].map(e=>(0,o.jsx)(a(),{href:`/grade/${e}`,children:(0,o.jsxs)(eG,{className:"p-6 hover:shadow-lg transition-all hover:scale-105 cursor-pointer group",children:[(0,o.jsxs)("div",{className:"text-3xl font-bold text-purple-600 mb-2 group-hover:text-purple-700",children:["Grade ",e]}),(0,o.jsx)("div",{className:"flex justify-center mb-2",children:[void 0,void 0,void 0,void 0,void 0].map((e,r)=>(0,o.jsx)(w,{className:"h-4 w-4 text-yellow-400 fill-current"},r))}),(0,o.jsx)("div",{className:"text-center",children:(0,o.jsx)(eq,{variant:"ghost",size:"sm",className:"text-xs",children:"Start Learning →"})})]})},e))})]})}),(0,o.jsx)("footer",{className:"bg-gray-800 text-white py-12 px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,o.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 p-2 rounded-lg",children:(0,o.jsx)(f,{className:"h-6 w-6 text-white"})}),(0,o.jsx)("span",{className:"text-2xl font-bold",children:"MathQuest"})]}),(0,o.jsx)("p",{className:"text-gray-400 mb-8",children:"Making mathematics accessible, engaging, and fun for every child."}),(0,o.jsx)("div",{className:"border-t border-gray-700 pt-8",children:(0,o.jsx)("p",{className:"text-gray-400",children:"\xa9 2024 MathQuest. All rights reserved."})})]})})]})}eG.displayName="Card",(0,n.forwardRef)(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eC("flex flex-col space-y-1.5 p-6",e),...r})).displayName="CardHeader",(0,n.forwardRef)(({className:e,...r},t)=>(0,o.jsx)("h3",{ref:t,className:eC("text-2xl font-semibold leading-none tracking-tight",e),...r})).displayName="CardTitle",(0,n.forwardRef)(({className:e,...r},t)=>(0,o.jsx)("p",{ref:t,className:eC("text-sm text-muted-foreground",e),...r})).displayName="CardDescription",(0,n.forwardRef)(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eC("p-6 pt-0",e),...r})).displayName="CardContent",(0,n.forwardRef)(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eC("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,70,567],()=>t(9394));module.exports=o})();