@echo off
REM MathQuest Complete Setup Script for Windows
REM This script sets up the complete MathQuest application with all data

echo 🎯 MathQuest Complete Setup
echo ==========================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

REM Check if MongoDB shell is available
mongosh --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  MongoDB shell (mongosh) not found. Please install MongoDB.
    echo    You can also use Docker: docker run -d -p 27017:27017 --name mongodb mongo:7.0
    echo.
)

echo 📦 Installing dependencies...
call npm install

echo.
echo 🗄️  Setting up database with comprehensive data...

REM Check if MongoDB is accessible
mongosh --eval "db.runCommand('ping')" --quiet >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MongoDB is running
    echo 📊 Initializing database with complete curriculum and sample data...
    mongosh mathquest ..\init-mongo.js
    echo ✅ Database initialized successfully!
) else (
    echo ⚠️  MongoDB is not accessible. Please ensure MongoDB is running.
    echo    You can start MongoDB with: mongod
    echo    Or use Docker: docker run -d -p 27017:27017 --name mongodb mongo:7.0
    echo.
    echo    After starting MongoDB, run: mongosh mathquest ..\init-mongo.js
)

echo.
echo 🚀 Starting the development server...
echo.
echo 📚 What's included in this setup:
echo    ✅ Complete curriculum for grades 1-10 (400 weeks total)
echo    ✅ 1000+ quiz questions across all topics
echo    ✅ 16+ achievements with multi-tier rewards
echo    ✅ 5 sample students with realistic progress
echo    ✅ 1 sample teacher account
echo    ✅ 80+ progress records for demonstration
echo.
echo 🎮 Sample login credentials:
echo    Student: <EMAIL> (Grade 1)
echo    Student: <EMAIL> (Grade 2)
echo    Student: <EMAIL> (Grade 3)
echo    Teacher: <EMAIL>
echo    Password for all: password123
echo.
echo 🌐 Starting application at http://localhost:3000
echo.

REM Start the development server
call npm run dev

pause
