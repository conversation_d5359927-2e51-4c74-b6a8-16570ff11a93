// MongoDB Initialization Script for MathQuest
// This script sets up the initial database structure and comprehensive sample data

// Switch to mathquest database
db = db.getSiblingDB('mathquest');

// Drop existing collections to start fresh
db.users.drop();
db.progress.drop();
db.achievements.drop();

// Create collections
db.createCollection('users');
db.createCollection('progress');
db.createCollection('achievements');

// Insert comprehensive achievements
db.achievements.insertMany([
  // Completion Achievements
  {
    id: 'first_lesson',
    name: 'First Steps',
    description: 'Complete your very first lesson',
    icon: '🎯',
    category: 'completion',
    points: 10,
    requirements: { type: 'lessons_completed', value: 1 },
    rarity: 'common',
    createdAt: new Date()
  },
  {
    id: 'week_warrior',
    name: 'Week Warrior',
    description: 'Complete all lessons in a week',
    icon: '⚔️',
    category: 'completion',
    points: 50,
    requirements: { type: 'week_completed', value: 1 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'month_master',
    name: 'Month Master',
    description: 'Complete 4 weeks of lessons',
    icon: '👑',
    category: 'completion',
    points: 200,
    requirements: { type: 'weeks_completed', value: 4 },
    rarity: 'epic',
    createdAt: new Date()
  },
  {
    id: 'grade_champion',
    name: 'Grade Champion',
    description: 'Complete an entire grade level',
    icon: '🏆',
    category: 'completion',
    points: 1000,
    requirements: { type: 'grade_completed', value: 1 },
    rarity: 'legendary',
    createdAt: new Date()
  },
  // Score Achievements
  {
    id: 'perfect_score',
    name: 'Perfect Score',
    description: 'Get 100% on any quiz',
    icon: '💯',
    category: 'score',
    points: 25,
    requirements: { type: 'perfect_score', value: 100 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'high_achiever',
    name: 'High Achiever',
    description: 'Score 90% or higher on 5 quizzes',
    icon: '⭐',
    category: 'score',
    points: 75,
    requirements: { type: 'high_scores', value: 5 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'perfectionist',
    name: 'Perfectionist',
    description: 'Get perfect scores on 10 quizzes',
    icon: '🌟',
    category: 'score',
    points: 250,
    requirements: { type: 'perfect_scores', value: 10 },
    rarity: 'epic',
    createdAt: new Date()
  },
  // Streak Achievements
  {
    id: 'daily_learner',
    name: 'Daily Learner',
    description: 'Complete lessons for 3 days in a row',
    icon: '📅',
    category: 'streak',
    points: 30,
    requirements: { type: 'daily_streak', value: 3 },
    rarity: 'common',
    createdAt: new Date()
  },
  {
    id: 'week_streak',
    name: 'Week Streak',
    description: 'Complete lessons for 7 days in a row',
    icon: '🔥',
    category: 'streak',
    points: 100,
    requirements: { type: 'daily_streak', value: 7 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'unstoppable',
    name: 'Unstoppable',
    description: 'Complete lessons for 30 days in a row',
    icon: '🚀',
    category: 'streak',
    points: 500,
    requirements: { type: 'daily_streak', value: 30 },
    rarity: 'legendary',
    createdAt: new Date()
  },
  // Time Achievements
  {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Complete a quiz in under 5 minutes',
    icon: '⚡',
    category: 'time',
    points: 40,
    requirements: { type: 'quiz_time', value: 300 },
    rarity: 'rare',
    createdAt: new Date()
  },
  {
    id: 'marathon_learner',
    name: 'Marathon Learner',
    description: 'Study for 60 minutes in one session',
    icon: '🏃',
    category: 'time',
    points: 80,
    requirements: { type: 'session_time', value: 3600 },
    rarity: 'epic',
    createdAt: new Date()
  },
  // Mastery Achievements
  {
    id: 'counting_master',
    name: 'Counting Master',
    description: 'Master all counting lessons in Grade 1',
    icon: '🔢',
    category: 'mastery',
    points: 150,
    requirements: { type: 'topic_mastery', value: 100, grade: 1, subject: 'counting' },
    rarity: 'epic',
    createdAt: new Date()
  },
  {
    id: 'addition_ace',
    name: 'Addition Ace',
    description: 'Master all addition lessons in Grade 1',
    icon: '➕',
    category: 'mastery',
    points: 150,
    requirements: { type: 'topic_mastery', value: 100, grade: 1, subject: 'addition' },
    rarity: 'epic',
    createdAt: new Date()
  },
  {
    id: 'multiplication_master',
    name: 'Multiplication Master',
    description: 'Master multiplication tables 1-10',
    icon: '✖️',
    category: 'mastery',
    points: 300,
    requirements: { type: 'topic_mastery', value: 100, grade: 3, subject: 'multiplication' },
    rarity: 'legendary',
    createdAt: new Date()
  }
]);

// Insert sample users
db.users.insertMany([
  {
    _id: 'user1',
    name: 'Emma Johnson',
    email: '<EMAIL>',
    password: '$2b$10$hashedpassword1', // In production, use proper password hashing
    role: 'student',
    grade: 1,
    avatar: '👧',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'user2',
    name: 'Liam Smith',
    email: '<EMAIL>',
    password: '$2b$10$hashedpassword2',
    role: 'student',
    grade: 2,
    avatar: '👦',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'user3',
    name: 'Sophia Davis',
    email: '<EMAIL>',
    password: '$2b$10$hashedpassword3',
    role: 'student',
    grade: 3,
    avatar: '👧',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'user4',
    name: 'Noah Wilson',
    email: '<EMAIL>',
    password: '$2b$10$hashedpassword4',
    role: 'student',
    grade: 4,
    avatar: '👦',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'user5',
    name: 'Olivia Brown',
    email: '<EMAIL>',
    password: '$2b$10$hashedpassword5',
    role: 'student',
    grade: 5,
    avatar: '👧',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: 'teacher1',
    name: 'Ms. Anderson',
    email: '<EMAIL>',
    password: '$2b$10$hashedpassword6',
    role: 'teacher',
    avatar: '👩‍🏫',
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Generate and insert sample progress data
var progressData = [];
var userProgressMap = {
  'user1': { completedWeeks: 8, grade: 1 },
  'user2': { completedWeeks: 12, grade: 2 },
  'user3': { completedWeeks: 15, grade: 3 },
  'user4': { completedWeeks: 20, grade: 4 },
  'user5': { completedWeeks: 25, grade: 5 }
};

Object.keys(userProgressMap).forEach(function(userId) {
  var userData = userProgressMap[userId];
  for (var week = 1; week <= userData.completedWeeks; week++) {
    var topicId = 'grade' + userData.grade + '_week' + week;
    var baseScore = 70 + Math.random() * 30;
    var timeSpent = 15 + Math.random() * 25;
    var attempts = Math.random() < 0.8 ? 1 : Math.floor(Math.random() * 3) + 1;

    progressData.push({
      userId: userId,
      grade: userData.grade,
      week: week,
      topicId: topicId,
      completed: true,
      score: Math.round(baseScore),
      timeSpent: Math.round(timeSpent),
      attempts: attempts,
      lastAttemptDate: new Date(Date.now() - (userData.completedWeeks - week) * 7 * 24 * 60 * 60 * 1000),
      achievements: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }
});

if (progressData.length > 0) {
  db.progress.insertMany(progressData);
}

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1, grade: 1 });
db.achievements.createIndex({ id: 1 }, { unique: true });
db.achievements.createIndex({ category: 1, rarity: 1 });
db.progress.createIndex({ userId: 1, grade: 1, week: 1 });
db.progress.createIndex({ userId: 1, completed: 1 });

print('🎉 MongoDB initialization completed successfully!');
print('');
print('Sample data summary:');
print('- 6 sample users created (5 students + 1 teacher)');
print('- 16 comprehensive achievements defined');
print('- ' + progressData.length + ' progress records generated');
print('- Database indexes created for optimal performance');
print('');
print('You can now start the MathQuest application!');
