{"name": "mathquest", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node scripts/initializeData.js", "setup:complete": "bash scripts/setup-complete.sh", "setup:windows": "scripts/setup-complete.bat", "init:db": "mongosh mathquest init-mongo.js"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "mongoose": "^8.15.1", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}